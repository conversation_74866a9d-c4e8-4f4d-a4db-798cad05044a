# 신호 관련 API 라우트
# routes/signal_routes.py
import logging
from datetime import datetime, timezone, timedelta
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from database import db_session
from models.user import User
from models.signal import Signal

logger = logging.getLogger("opensystems_bot")

signal_bp = Blueprint('signal', __name__)

@signal_bp.route('/history', methods=['GET'])
@jwt_required()
def get_signal_history():
    """신호 히스토리 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        # 쿼리 파라미터
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        days = request.args.get('days', 7, type=int)  # 기본 7일

        # 날짜 범위 계산
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)

        # 신호 조회
        signals_query = Signal.query.filter(
            Signal.user_id == user.id,
            Signal.received_at >= start_date,
            Signal.received_at <= end_date
        ).order_by(Signal.received_at.desc())

        signals = signals_query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        signal_list = []
        for signal in signals.items:
            signal_data = signal.to_dict()
            signal_list.append(signal_data)

        return jsonify({
            'success': True,
            'signals': signal_list,
            'pagination': {
                'page': signals.page,
                'pages': signals.pages,
                'per_page': signals.per_page,
                'total': signals.total
            },
            'date_range': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        }), 200

    except Exception as e:
        logger.error(f"신호 히스토리 조회 오류: {e}")
        return jsonify({'error': '신호 히스토리 조회 중 오류가 발생했습니다'}), 500

@signal_bp.route('/latest', methods=['GET'])
@jwt_required()
def get_latest_signals():
    """최신 신호 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        limit = request.args.get('limit', 10, type=int)

        # 최신 신호 조회
        signals = Signal.query.filter_by(user_id=user.id)\
                             .order_by(Signal.received_at.desc())\
                             .limit(limit).all()

        signal_list = []
        for signal in signals:
            signal_data = signal.to_dict()
            signal_list.append(signal_data)

        return jsonify({
            'success': True,
            'signals': signal_list,
            'count': len(signal_list)
        }), 200

    except Exception as e:
        logger.error(f"최신 신호 조회 오류: {e}")
        return jsonify({'error': '최신 신호 조회 중 오류가 발생했습니다'}), 500

@signal_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_signal_stats():
    """신호 통계 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        days = request.args.get('days', 7, type=int)

        # 날짜 범위 계산
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)

        # 기본 통계
        total_signals = Signal.query.filter(
            Signal.user_id == user.id,
            Signal.received_at >= start_date
        ).count()

        processed_signals = Signal.query.filter(
            Signal.user_id == user.id,
            Signal.received_at >= start_date,
            Signal.is_processed == True
        ).count()

        # 색상별 통계
        signals = Signal.query.filter(
            Signal.user_id == user.id,
            Signal.received_at >= start_date
        ).all()

        color_stats = {
            'green': 0,
            'red': 0,
            'blue': 0,
            'yellow': 0,
            'other': 0
        }

        for signal in signals:
            colors = signal.get_colors()
            for color in colors:
                if color in color_stats:
                    color_stats[color] += 1
                else:
                    color_stats['other'] += 1

        processing_rate = (processed_signals / total_signals * 100) if total_signals > 0 else 0

        return jsonify({
            'success': True,
            'stats': {
                'total_signals': total_signals,
                'processed_signals': processed_signals,
                'processing_rate': round(processing_rate, 2),
                'color_stats': color_stats,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"신호 통계 조회 오류: {e}")
        return jsonify({'error': '신호 통계 조회 중 오류가 발생했습니다'}), 500

@signal_bp.route('/test', methods=['POST'])
@jwt_required()
def test_signal():
    """테스트 신호 생성"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '신호 데이터가 필요합니다'}), 400

        # 기본 테스트 신호 데이터
        test_signal_data = {
            'colors': data.get('colors', ['green']),
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'test': True,
            'source': 'manual_test'
        }

        # 신호 생성
        test_signal = Signal(
            user_id=user.id,
            symbol=data.get('symbol', 'BTCUSDT'),
            data=test_signal_data
        )

        db_session.add(test_signal)
        db_session.commit()

        logger.info(f"테스트 신호 생성: {user.username}, 색상: {test_signal_data['colors']}")

        return jsonify({
            'success': True,
            'message': '테스트 신호가 생성되었습니다',
            'signal': test_signal.to_dict()
        }), 201

    except Exception as e:
        logger.error(f"테스트 신호 생성 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '테스트 신호 생성 중 오류가 발생했습니다'}), 500
