@echo off
echo ========================================
echo OPENSYSTEMS BOT V3.1 - Server Start
echo ========================================
echo.

echo [1/3] Starting SignalR Server... (Port 5000)
start "SignalR Server" cmd /k "python signalr_server.py"
timeout /t 3 /nobreak >nul

echo [2/3] Starting Backend Server... (Port 3000)
start "Backend Server" cmd /k "python app.py"
timeout /t 3 /nobreak >nul

echo [3/3] Starting Flutter Web App... (Port 8080)
start "Flutter Web App" cmd /k "cd frontend && flutter run -d web-server --web-hostname=0.0.0.0 --web-port=8080"

echo.
echo ========================================
echo All servers started successfully!
echo ========================================
echo SignalR Server: http://localhost:5000
echo Backend Server: http://localhost:3000
echo Web App: http://localhost:8080
echo ========================================
echo.
echo To stop servers, run stop_servers.bat
pause
