# 데모/리얼 모드 관리
# bot_core/mode_manager.py
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from database import db_session
from models.user import User
from models.setting import Setting

logger = logging.getLogger("opensystems_bot")

class ModeManager:
    """데모/리얼 모드 관리자"""

    def __init__(self, user_id: int, api_client_class):
        self.user_id = user_id
        self.api_client_class = api_client_class
        self.current_client = None

        # 사용자 설정 로드
        self.user = User.query.get(user_id)
        if not self.user:
            raise ValueError(f"사용자를 찾을 수 없습니다: {user_id}")

        # 설정이 없으면 기본 설정 생성
        if not self.user.setting:
            self.user.setting = Setting(user_id=user_id)
            db_session.add(self.user.setting)
            db_session.commit()

        self.setting = self.user.setting
        logger.info(f"ModeManager 초기화 - 사용자: {user_id}, 모드: {'데모' if self.setting.is_demo else '리얼'}")

    def get_mode_info(self) -> Dict[str, Any]:
        """현재 모드 정보 조회"""
        return {
            'is_demo': self.setting.is_demo,
            'mode_name': '데모 모드' if self.setting.is_demo else '리얼 모드',
            'has_demo_api': self.setting.has_demo_api(),
            'has_real_api': self.setting.has_real_api(),
            'can_trade': self._can_trade(),
            'symbol': self.setting.symbol,
            'leverage': self.setting.leverage,
            'investment_ratio': self.setting.investment_ratio
        }

    def get_settings(self) -> Dict[str, Any]:
        """설정 정보 조회"""
        return {
            'is_demo': self.setting.is_demo,
            'has_demo_api': self.setting.has_demo_api(),
            'has_real_api': self.setting.has_real_api(),
            'symbol': self.setting.symbol,
            'leverage': self.setting.leverage,
            'investment_ratio': self.setting.investment_ratio
        }

    def switch_mode(self, to_demo: bool) -> Dict[str, Any]:
        """모드 전환"""
        try:
            old_mode = '데모' if self.setting.is_demo else '리얼'
            new_mode = '데모' if to_demo else '리얼'

            if self.setting.is_demo == to_demo:
                return {
                    'success': False,
                    'message': f'이미 {new_mode} 모드입니다'
                }

            # API 키 확인
            if to_demo and not self.setting.has_demo_api():
                return {
                    'success': False,
                    'message': '데모 모드 API 키가 설정되지 않았습니다'
                }

            if not to_demo and not self.setting.has_real_api():
                return {
                    'success': False,
                    'message': '리얼 모드 API 키가 설정되지 않았습니다'
                }

            # 기존 클라이언트 정리
            if self.current_client:
                self.current_client = None

            # 모드 전환
            self.setting.is_demo = to_demo
            self.setting.updated_at = datetime.utcnow()
            db_session.commit()

            logger.info(f"모드 전환 완료: {old_mode} -> {new_mode}")

            return {
                'success': True,
                'message': f'{old_mode} 모드에서 {new_mode} 모드로 전환되었습니다',
                'old_mode': old_mode,
                'new_mode': new_mode,
                'mode_info': self.get_mode_info()
            }

        except Exception as e:
            logger.error(f"모드 전환 오류: {e}")
            db_session.rollback()
            return {
                'success': False,
                'error': str(e)
            }

    def initialize_client(self):
        """현재 모드에 맞는 API 클라이언트 초기화"""
        try:
            if not self._can_trade():
                logger.warning("거래 불가능한 상태에서 클라이언트 초기화 시도")
                return None

            # API 키 가져오기
            if self.setting.is_demo:
                api_key = self.setting.demo_api_key
                api_secret = self.setting.demo_api_secret
            else:
                api_key = self.setting.real_api_key
                api_secret = self.setting.real_api_secret

            if not api_key or not api_secret:
                logger.error("API 키 또는 시크릿이 없습니다")
                return None

            # 클라이언트 생성
            self.current_client = self.api_client_class(
                api_key=api_key,
                api_secret=api_secret,
                is_demo=self.setting.is_demo
            )

            # 연결 테스트
            if self.current_client.test_connection():
                logger.info(f"API 클라이언트 초기화 성공 - {'데모' if self.setting.is_demo else '리얼'} 모드")
                return self.current_client
            else:
                logger.error("API 연결 테스트 실패")
                self.current_client = None
                return None

        except Exception as e:
            logger.error(f"API 클라이언트 초기화 오류: {e}")
            self.current_client = None
            return None

    def get_client(self):
        """현재 API 클라이언트 반환"""
        if not self.current_client:
            return self.initialize_client()
        return self.current_client

    def update_settings(self, **kwargs) -> Dict[str, Any]:
        """설정 업데이트"""
        try:
            updated_fields = []

            # 기본 설정 업데이트
            if 'symbol' in kwargs:
                self.setting.symbol = kwargs['symbol']
                updated_fields.append('symbol')

            if 'leverage' in kwargs:
                self.setting.leverage = kwargs['leverage']
                updated_fields.append('leverage')

            if 'investment_ratio' in kwargs:
                self.setting.investment_ratio = kwargs['investment_ratio']
                updated_fields.append('investment_ratio')

            if 'hedging_threshold' in kwargs:
                self.setting.hedging_threshold = kwargs['hedging_threshold']
                updated_fields.append('hedging_threshold')

            # API 키 업데이트
            if 'demo_api_key' in kwargs:
                self.setting.demo_api_key = kwargs['demo_api_key']
                updated_fields.append('demo_api_key')

            if 'demo_api_secret' in kwargs:
                self.setting.demo_api_secret = kwargs['demo_api_secret']
                updated_fields.append('demo_api_secret')

            if 'real_api_key' in kwargs:
                self.setting.real_api_key = kwargs['real_api_key']
                updated_fields.append('real_api_key')

            if 'real_api_secret' in kwargs:
                self.setting.real_api_secret = kwargs['real_api_secret']
                updated_fields.append('real_api_secret')

            if updated_fields:
                self.setting.updated_at = datetime.utcnow()
                db_session.commit()

                # API 키가 변경된 경우 클라이언트 재초기화
                if any(field.endswith('api_key') or field.endswith('api_secret') for field in updated_fields):
                    self.current_client = None

                logger.info(f"설정 업데이트 완료: {updated_fields}")

                return {
                    'success': True,
                    'message': '설정이 업데이트되었습니다',
                    'updated_fields': updated_fields,
                    'settings': self.get_settings()
                }
            else:
                return {
                    'success': False,
                    'message': '업데이트할 설정이 없습니다'
                }

        except Exception as e:
            logger.error(f"설정 업데이트 오류: {e}")
            db_session.rollback()
            return {
                'success': False,
                'error': str(e)
            }

    def _can_trade(self) -> bool:
        """거래 가능 여부 확인"""
        if self.setting.is_demo:
            return self.setting.has_demo_api()
        else:
            return self.setting.has_real_api()

    def get_account_info(self) -> Dict[str, Any]:
        """계정 정보 조회"""
        try:
            client = self.get_client()
            if not client:
                return {
                    'error': 'API 클라이언트를 초기화할 수 없습니다',
                    'mode_info': self.get_mode_info()
                }

            account_info = client.get_account_info()
            account_info['mode_info'] = self.get_mode_info()

            return account_info

        except Exception as e:
            logger.error(f"계정 정보 조회 오류: {e}")
            return {
                'error': str(e),
                'mode_info': self.get_mode_info()
            }
