# 수익 추적 모델
# models/profit_tracking.py
import datetime
from sqlalchemy import Column, Integer, Float, String, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from database import Base

class ProfitTracking(Base):
    """수익 추적 모델"""
    __tablename__ = 'profit_tracking'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    initial_balance = Column(Float, nullable=False)  # 기준 잔고
    initial_equity = Column(Float, nullable=False)   # 기준 총자산
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.datetime.utcnow)
    is_active = Column(Boolean, default=True)  # 활성 상태
    reset_reason = Column(String(100))  # 리셋 사유
    session_count = Column(Integer, default=1)  # 세션 카운트
    
    # 관계 설정
    user = relationship("User", back_populates="profit_tracking")
    
    def __init__(self, user_id, initial_balance, initial_equity, reset_reason=None):
        """
        수익 추적 초기화
        
        Args:
            user_id: 사용자 ID
            initial_balance: 기준 잔고
            initial_equity: 기준 총자산
            reset_reason: 리셋 사유
        """
        self.user_id = user_id
        self.initial_balance = initial_balance
        self.initial_equity = initial_equity
        self.reset_reason = reset_reason or "Initial Setup"
        self.created_at = datetime.datetime.utcnow()
        self.is_active = True
        self.session_count = 1
    
    def calculate_profit_percentage(self, current_equity):
        """
        수익률 계산
        
        Args:
            current_equity: 현재 총자산
            
        Returns:
            float: 수익률 (백분율)
        """
        if self.initial_equity <= 0:
            return 0.0
            
        profit_percentage = ((current_equity - self.initial_equity) / self.initial_equity) * 100
        return round(profit_percentage, 2)
    
    def calculate_profit_amount(self, current_equity):
        """
        수익 금액 계산
        
        Args:
            current_equity: 현재 총자산
            
        Returns:
            float: 수익 금액
        """
        return round(current_equity - self.initial_equity, 2)
    
    def reset_tracking(self, new_balance, new_equity, reason="Manual Reset"):
        """
        수익 추적 리셋
        
        Args:
            new_balance: 새로운 기준 잔고
            new_equity: 새로운 기준 총자산
            reason: 리셋 사유
        """
        self.initial_balance = new_balance
        self.initial_equity = new_equity
        self.reset_reason = reason
        self.updated_at = datetime.datetime.utcnow()
        self.session_count += 1
    
    def deactivate(self):
        """수익 추적 비활성화"""
        self.is_active = False
        self.updated_at = datetime.datetime.utcnow()
    
    def __repr__(self):
        return f"<ProfitTracking(user_id={self.user_id}, initial_equity={self.initial_equity}, active={self.is_active})>"
