# OPENSYSTEMS BOT V3 - Trading Platform

Flutter 기반 암호화폐 트레이딩 봇 플랫폼입니다.

## 프로젝트 구조

### 서버 시작 명령어

#### SignalR 서버 (포트 5000)
```bash
# SignalR 서버 시작
python signalr_server.py
```

#### 백엔드 서버 (포트 3000)
```bash
# Flask 백엔드 서버 시작
python app.py
```

#### 웹 앱 (포트 8080)
```bash
# Flutter 웹 앱 시작
flutter run -d web-server --web-hostname=0.0.0.0 --web-port=8080
```

### 데이터베이스 구조 (db/ 폴더)
- `users.json` - 사용자 정보 및 프로필
- `api_keys.json` - 거래소 API 키 정보
- `bot_settings.json` - 봇 설정 정보
- `bot_status.json` - 봇 상태 정보
- `trades.json` - 거래 내역 데이터
- `signals.json` - 신호 데이터
- `logs.json` - 시스템 로그 데이터
- `ranking.json` - 사용자 순위 데이터
- `market_data.json` - 시장 데이터
- `sessions.json` - 사용자 세션 정보
- `statistics.json` - 거래 통계 데이터
- `notifications.json` - 알림 데이터
- `system_config.json` - 시스템 설정 정보
- `index.json` - 데이터베이스 구조 인덱스

## 기능
- 반응형 웹 디자인 (모바일/데스크톱 지원)
- 실시간 트레이딩 신호
- 사용자 랭킹 시스템
- 거래 내역 추적
- 시스템 로그 관
## 시작하기
1. Flutter 의존성 설치: `flutter pub get`
2. 서버 시작:
   - SignalR 서버: `python signalr_server.py` (포트 5000)
   - 백엔드 서버: `python app.py` (포트 3000)
   - 웹 앱: `flutter run -d web-server --web-hostname=0.0.0.0 --web-port=8080` (포트 8080)
3. 브라우저에서 `http://localhost:8080` 또는 `http://[Your-IP]:8080` 접속

## 배치파일 사용
- 모든 서버 시작: `start_servers.bat`
- 모든 서버 정지: `stop_servers.bat`
