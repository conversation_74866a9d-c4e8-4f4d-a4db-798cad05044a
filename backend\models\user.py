# 사용자 모델
# models/user.py
import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.orm import relationship
from werkzeug.security import generate_password_hash, check_password_hash

from database import Base
from utils.helpers import utc_now

class User(Base):
    """사용자 모델"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), nullable=False, unique=True)
    email = Column(String(100), nullable=False, unique=True)
    password_hash = Column(String(128), nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    admin_level = Column(Integer, default=0)  # 0=일반, 1=읽기, 2=일반관리, 3=최고관리자
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.datetime.utcnow)
    last_login = Column(DateTime)
    
    # 관계 설정
    setting = relationship("Setting", uselist=False, back_populates="user", cascade="all, delete-orphan")
    logs = relationship("Log", back_populates="user", cascade="all, delete-orphan")
    signals = relationship("Signal", back_populates="user", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="user", cascade="all, delete-orphan")
    positions = relationship("Position", back_populates="user", cascade="all, delete-orphan")
    profit_tracking = relationship("ProfitTracking", back_populates="user", cascade="all, delete-orphan")
    
    def __init__(self, username, email, password, is_admin=False, admin_level=0):
        """
        사용자 초기화
        
        Args:
            username: 사용자명
            email: 이메일
            password: 비밀번호 (평문)
            is_admin: 관리자 여부
            admin_level: 관리자 레벨 (0=일반, 1=읽기, 2=일반관리, 3=최고관리자)
        """
        self.username = username
        self.email = email
        self.set_password(password)
        self.is_admin = is_admin
        self.admin_level = admin_level
        self.created_at = utc_now()
    
    def set_password(self, password):
        """비밀번호 설정"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """비밀번호 확인"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """마지막 로그인 시간 업데이트"""
        self.last_login = utc_now()
    
    def to_dict(self, include_admin=False):
        """
        사용자 정보를 딕셔너리로 변환
        
        Args:
            include_admin: 관리자 정보 포함 여부
            
        Returns:
            사용자 정보 딕셔너리
        """
        result = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
        
        if include_admin:
            result.update({
                'is_admin': self.is_admin,
                'admin_level': self.admin_level
            })
        
        return result
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}', is_admin={self.is_admin})>"