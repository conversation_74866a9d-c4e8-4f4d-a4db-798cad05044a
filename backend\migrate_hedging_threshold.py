#!/usr/bin/env python3
# 헷징 임계값 컬럼 추가 마이그레이션 스크립트
import sqlite3
import os

def migrate_hedging_threshold():
    """안전하게 hedging_threshold 컬럼 추가"""
    db_path = "db/opensystems_bot.db"
    
    if not os.path.exists(db_path):
        print("❌ 데이터베이스 파일이 존재하지 않습니다.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 헷징 임계값 컬럼 추가 시작...")
        print("=" * 50)
        
        # 1. 백업 확인
        print("📋 현재 settings 테이블 데이터 백업 확인:")
        cursor.execute("SELECT COUNT(*) FROM settings;")
        record_count = cursor.fetchone()[0]
        print(f"  총 {record_count}개의 설정 레코드 존재")
        
        # 2. hedging_threshold 컬럼 존재 여부 재확인
        cursor.execute("PRAGMA table_info(settings);")
        columns = cursor.fetchall()
        hedging_column_exists = any(col[1] == 'hedging_threshold' for col in columns)
        
        if hedging_column_exists:
            print("✅ hedging_threshold 컬럼이 이미 존재합니다.")
            conn.close()
            return True
        
        # 3. ALTER TABLE로 컬럼 추가
        print("🔧 ALTER TABLE 실행 중...")
        alter_sql = "ALTER TABLE settings ADD COLUMN hedging_threshold VARCHAR(10) DEFAULT '-1.0%';"
        cursor.execute(alter_sql)
        
        # 4. 변경사항 커밋
        conn.commit()
        print("✅ ALTER TABLE 성공!")
        
        # 5. 결과 확인
        print("\n📊 변경 후 테이블 구조:")
        cursor.execute("PRAGMA table_info(settings);")
        new_columns = cursor.fetchall()
        
        for col in new_columns:
            if col[1] == 'hedging_threshold':
                print(f"  ✅ {col[1]} ({col[2]}) DEFAULT: {col[4]}")
        
        # 6. 데이터 무결성 확인
        print("\n🔍 데이터 무결성 확인:")
        cursor.execute("SELECT COUNT(*) FROM settings;")
        new_record_count = cursor.fetchone()[0]
        
        if new_record_count == record_count:
            print(f"  ✅ 데이터 보존 확인: {record_count}개 레코드 유지")
        else:
            print(f"  ❌ 데이터 손실 발생: {record_count} → {new_record_count}")
            conn.close()
            return False
        
        # 7. 새 컬럼 값 확인
        cursor.execute("SELECT id, user_id, hedging_threshold FROM settings;")
        rows = cursor.fetchall()
        
        print("  새로 추가된 hedging_threshold 값:")
        for row in rows:
            print(f"    사용자 {row[1]}: {row[2]}")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("🎯 마이그레이션 완료!")
        print("✅ 기존 데이터 보존")
        print("✅ hedging_threshold 컬럼 추가")
        print("✅ 기본값 -1.0% 설정")
        
        return True
        
    except Exception as e:
        print(f"❌ 마이그레이션 오류: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    success = migrate_hedging_threshold()
    if success:
        print("\n🚀 이제 백엔드를 재시작하면 헷징 임계값 설정이 정상 작동합니다!")
    else:
        print("\n❌ 마이그레이션 실패. 수동 확인이 필요합니다.")
