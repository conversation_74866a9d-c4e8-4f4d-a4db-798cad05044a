# database.py
import os
import json
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager

# 데이터베이스 URL 설정
DB_PATH = os.environ.get('DB_PATH', 'db')
SQLALCHEMY_DATABASE_URL = os.environ.get('DATABASE_URL', f'sqlite:///{DB_PATH}/opensystems_bot.db')

# 로깅 설정
logger = logging.getLogger("opensystems_bot")

# SQLAlchemy 엔진 생성
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False} if SQLALCHEMY_DATABASE_URL.startswith('sqlite') else {}
)

# 세션 팩토리 생성
session_factory = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 스코프 세션 생성 (스레드 안전)
db_session = scoped_session(session_factory)

# 베이스 클래스 생성
Base = declarative_base()
Base.query = db_session.query_property()

def init_db():
    """데이터베이스 초기화"""
    try:
        # 스키마 생성
        Base.metadata.create_all(bind=engine)
        logger.info("Database schema created successfully")

        # 데이터베이스 디렉토리 확인 및 생성 (SQLite 파일용)
        if not os.path.exists(DB_PATH):
            os.makedirs(DB_PATH)
            logger.info(f"Created database directory: {DB_PATH}")

        logger.info("Database initialization completed")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

# JSON 파일 관련 함수들 - 현재 사용되지 않음 (SQLite 통합으로 인해)
# def ensure_json_files():
#     """필요한 JSON 파일이 있는지 확인하고 없으면 생성"""
#     json_files = [
#         'api_keys.json',
#         'bot_settings.json',
#         'bot_status.json',
#         'index.json',
#         'logs.json',
#         'market_data.json',
#         'notifications.json',
#         'ranking.json',
#         'sessions.json',
#         'signals.json',
#         'statistics.json',
#         'system_config.json',
#         'trades.json',
#         'users.json'
#     ]
#
#     for file_name in json_files:
#         file_path = os.path.join(DB_PATH, file_name)
#         if not os.path.exists(file_path):
#             # 파일이 없으면 기본 구조로 생성
#             with open(file_path, 'w') as f:
#                 json.dump(get_default_structure(file_name), f, indent=2)
#             logger.info(f"Created JSON file: {file_name}")

# def get_default_structure(file_name):
#     """JSON 파일의 기본 구조 반환"""
#     if file_name == 'index.json':
#         return {"last_updated": None, "files": []}
#     elif file_name == 'users.json':
#         return {"users": []}
#     elif file_name == 'api_keys.json':
#         return {"api_keys": []}
#     elif file_name == 'bot_settings.json':
#         return {"settings": []}
#     elif file_name == 'bot_status.json':
#         return {"status": []}
#     elif file_name == 'logs.json':
#         return {"logs": []}
#     elif file_name == 'signals.json':
#         return {"signals": []}
#     elif file_name == 'trades.json':
#         return {"trades": []}
#     elif file_name == 'market_data.json':
#         return {"market_data": []}
#     elif file_name == 'statistics.json':
#         return {"statistics": []}
#     elif file_name == 'notifications.json':
#         return {"notifications": []}
#     elif file_name == 'ranking.json':
#         return {"ranking": []}
#     elif file_name == 'sessions.json':
#         return {"sessions": []}
#     elif file_name == 'system_config.json':
#         return {
#             "system": {
#                 "version": "1.0.0",
#                 "environment": "development",
#                 "signal_url": "https://signalr.example.com/trading",
#                 "bybit_demo_url": "https://api-demo.bybit.com",
#                 "bybit_real_url": "https://api.bybit.com"
#             }
#         }
#     # 기본값은 빈 객체
#     return {}

# @contextmanager
# def get_json_db(file_name):
#     """JSON 파일을 읽고 쓰기 위한 컨텍스트 매니저"""
#     file_path = os.path.join(DB_PATH, file_name)
#
#     try:
#         # 파일이 없으면 생성
#         if not os.path.exists(file_path):
#             with open(file_path, 'w') as f:
#                 json.dump(get_default_structure(file_name), f, indent=2)
#
#         # 파일 읽기
#         with open(file_path, 'r') as f:
#             data = json.load(f)
#
#         # 컨텍스트 제공
#         yield data
#
#         # 변경된 데이터 저장
#         with open(file_path, 'w') as f:
#             json.dump(data, f, indent=2)
#
#     except Exception as e:
#         logger.error(f"Error accessing JSON database {file_name}: {e}")
#         raise

# def read_json_db(file_name):
#     """JSON 파일 읽기"""
#     try:
#         with get_json_db(file_name) as data:
#             return data
#     except Exception as e:
#         logger.error(f"Error reading JSON database {file_name}: {e}")
#         return get_default_structure(file_name)

# def write_json_db(file_name, data):
#     """JSON 파일 쓰기"""
#     file_path = os.path.join(DB_PATH, file_name)
#     try:
#         with open(file_path, 'w') as f:
#             json.dump(data, f, indent=2)
#         return True
#     except Exception as e:
#         logger.error(f"Error writing JSON database {file_name}: {e}")
#         return False

# def update_json_db(file_name, update_func):
#     """JSON 파일 업데이트"""
#     try:
#         with get_json_db(file_name) as data:
#             update_func(data)
#         return True
#     except Exception as e:
#         logger.error(f"Error updating JSON database {file_name}: {e}")
#         return False