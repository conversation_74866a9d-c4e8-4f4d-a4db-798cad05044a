@echo off
echo ========================================
echo OPENSYSTEMS BOT V3.1 - Background Start
echo ========================================
echo.

echo [1/3] Starting SignalR Server in Background... (Port 5000)
cd 25_06_11\SignalRServer
start /B "" SignalRServer.exe
cd ..\..
timeout /t 3 /nobreak >nul

echo [2/3] Starting Backend Server in Background... (Port 3000)
cd backend
start /B "" python app.py
cd ..
timeout /t 3 /nobreak >nul

echo [3/3] Starting Flutter Web App in Background... (Port 8080)
cd frontend
start /B "" flutter run -d web-server --web-hostname=0.0.0.0 --web-port=8080 --release
cd ..

echo.
echo ========================================
echo All servers started in BACKGROUND!
echo ========================================
echo SignalR Server: http://localhost:5000
echo Backend Server: http://localhost:3000
echo Web App: http://localhost:8080
echo ========================================
echo.
echo Services are running in background.
echo To stop servers, run stop_servers.bat
echo You can close this window safely.
echo ========================================
pause
