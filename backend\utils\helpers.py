# Utility Functions
# utils/helpers.py
import os
import json
import uuid
import hashlib
import re
from datetime import datetime, timezone
import logging

logger = logging.getLogger("opensystems_bot")

def generate_id():
    """고유 ID 생성"""
    return str(uuid.uuid4())

def hash_password(password):
    """비밀번호 해싱"""
    salt = os.environ.get('PASSWORD_SALT', 'opensystems_password_salt')
    return hashlib.sha256(f"{password}{salt}".encode()).hexdigest()

def verify_password(hashed_password, password):
    """비밀번호 검증"""
    return hashed_password == hash_password(password)

def is_valid_email(email):
    """이메일 형식 검증"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def is_valid_password(password):
    """비밀번호 강도 검증"""
    # 최소 8자 이상, 하나 이상의 문자와 숫자 포함
    if len(password) < 8:
        return False
    if not re.search(r'[A-Za-z]', password):
        return False
    if not re.search(r'[0-9]', password):
        return False
    return True

def format_number(number, decimals=2):
    """숫자 포맷팅"""
    try:
        return "{:,.{decimals}f}".format(number, decimals=decimals)
    except Exception:
        return str(number)

def utc_now():
    """현재 UTC 시간"""
    return datetime.now(timezone.utc)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """날짜/시간 포맷팅"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime(format_str)
    
    return str(dt)

def parse_datetime(dt_str):
    """날짜/시간 문자열 파싱"""
    try:
        return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
    except ValueError:
        try:
            return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None

def load_json_file(file_path, default=None):
    """JSON 파일 로드"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                return json.load(f)
        return default if default is not None else {}
    except Exception as e:
        logger.error(f"Error loading JSON file {file_path}: {e}")
        return default if default is not None else {}

def save_json_file(file_path, data):
    """JSON 파일 저장"""
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving JSON file {file_path}: {e}")
        return False

def merge_dicts(dict1, dict2):
    """두 딕셔너리 병합"""
    result = dict1.copy()
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
    return result

def parse_leverage(leverage):
    """레버리지 문자열 파싱"""
    if isinstance(leverage, (int, float)):
        return int(leverage)
    
    if isinstance(leverage, str):
        # "5X" 형태의 문자열에서 숫자만 추출
        match = re.search(r'(\d+)', leverage)
        if match:
            return int(match.group(1))
    
    # 기본값
    return 5

def parse_investment_ratio(ratio):
    """투자 비율 문자열 파싱"""
    if isinstance(ratio, (int, float)):
        return float(ratio) / 100.0 if ratio > 1.0 else float(ratio)

    if isinstance(ratio, str):
        # "25%" 형태의 문자열에서 숫자만 추출
        match = re.search(r'(\d+)', ratio)
        if match:
            return int(match.group(1)) / 100.0

    # 기본값
    return 0.25

def parse_hedging_threshold(threshold):
    """🟢 헷징 임계값 문자열 파싱 (예: "-1.0%" → -1.0)"""
    if isinstance(threshold, (int, float)):
        return float(threshold)

    if isinstance(threshold, str):
        # "-1.0%" 형태의 문자열에서 숫자 추출
        match = re.search(r'(-?\d+\.?\d*)', threshold)
        if match:
            return float(match.group(1))

    # 기본값: -1.0%
    return -1.0

def format_pnl(pnl):
    """손익률 포맷팅"""
    if pnl > 0:
        return f"+{format_number(pnl)}%"
    return f"{format_number(pnl)}%"

def safe_float(value, default=0.0):
    """안전하게 float 변환"""
    try:
        return float(value)
    except (TypeError, ValueError):
        return default

def safe_int(value, default=0):
    """안전하게 int 변환"""
    try:
        return int(value)
    except (TypeError, ValueError):
        return default

def to_camel_case(snake_str):
    """스네이크 케이스를 카멜 케이스로 변환"""
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def to_snake_case(camel_str):
    """카멜 케이스를 스네이크 케이스로 변환"""
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()