# 매매 로직 핵심 구현
# bot_core/trading_logic.py
import logging
import threading
import time
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Optional, Any

from database import db_session
from models.position import Position
from models.trade import Trade
from models.signal import Signal
from utils.helpers import generate_id, parse_investment_ratio, parse_hedging_threshold

logger = logging.getLogger("opensystems_bot")

class PositionType(Enum):
    """포지션 타입"""
    IDLE = "IDLE"
    WAITING_LONG_SECONDARY = "WAITING_LONG_SECONDARY"
    WAITING_SHORT_SECONDARY = "WAITING_SHORT_SECONDARY"
    LONG_ACTIVE = "LONG_ACTIVE"
    SHORT_ACTIVE = "SHORT_ACTIVE"
    BIDIRECTIONAL_ACTIVE = "BIDIRECTIONAL_ACTIVE"

class TradingLogic:
    """색상 검출 신호 기반 매매 로직"""

    def __init__(self, user_id: int, api_client, symbol: str = "BTCUSDT", leverage: int = 10, investment_ratio: str = "100%", hedging_threshold: str = "-1.0%", signal_processor=None):
        self.user_id = user_id
        self.api_client = api_client
        self.symbol = symbol
        self.leverage = leverage  # 레버리지 배수
        self.stop_loss_percentage = -0.5  # -0.5% 손실 감지
        self.signal_processor = signal_processor  # SignalR 거래 로그 전송을 위한 참조

        # Investment Ratio를 숫자로 변환 (예: "100%" → 1.0, "50%" → 0.5)
        self.investment_ratio = parse_investment_ratio(investment_ratio)
        # 🟢 단방향 모드: 전체 투자 비율 사용 (분할 없음)

        # 🟢 헷징 임계값을 숫자로 변환 (예: "-1.0%" → -1.0)
        self.hedging_threshold = parse_hedging_threshold(hedging_threshold)

        # 현재 포지션 정보
        self.current_positions = {
            'long': None,
            'short': None
        }

        # 업데이트된 전략 상태 관리
        self.position_status = "waiting"  # waiting/long_only/short_only/hedging_long_short/hedging_short_long
        self.is_hedging = False
        self.hedging_positions = {}
        self.pending_orders = {}  # 대기 중인 지정가 주문들
        # 🔴 기존 하드코딩된 헷징 임계값 제거 (위에서 사용자 설정값으로 대체됨)

        # 봇 실행 상태 관리 (중요!)
        self.is_running = False

        # 포지션 모니터링 스레드 관리
        self.position_monitor_thread = None
        self.position_monitor_running = False
        self.position_monitor_stop_event = threading.Event()

        # Total Profit 추적 관리
        self.profit_tracking = None
        self.profit_tracking_initialized = False

        # Total Profit 초기화
        self._initialize_profit_tracking()

        logger.info(f"TradingLogic 초기화 - 사용자: {user_id}, 심볼: {symbol}, 레버리지: {leverage}X, 투자비율: {investment_ratio} ({self.investment_ratio*100:.0f}%), 헷징임계값: {hedging_threshold} ({self.hedging_threshold:.1f}%)")

    def update_settings(self, symbol: str = None, leverage: int = None, investment_ratio: str = None, hedging_threshold: str = None) -> Dict[str, Any]:
        """🟢 실행 중인 봇의 설정값 동적 업데이트"""
        try:
            updated_fields = []
            old_values = {}

            # 기존 값 백업
            old_values['symbol'] = self.symbol
            old_values['leverage'] = self.leverage
            old_values['investment_ratio'] = f"{self.investment_ratio*100:.0f}%"
            old_values['hedging_threshold'] = f"{self.hedging_threshold:.1f}%"

            # 심볼 업데이트
            if symbol and symbol != self.symbol:
                self.symbol = symbol
                updated_fields.append('symbol')

            # 레버리지 업데이트
            if leverage and leverage != self.leverage:
                self.leverage = leverage
                updated_fields.append('leverage')

                # 🟢 즉시 바이비트에 레버리지 설정 적용
                if hasattr(self, 'api_client') and self.api_client:
                    leverage_set = self.api_client.set_leverage(self.symbol, self.leverage)
                    if leverage_set:
                        logger.info(f"✅ 레버리지 즉시 적용 완료: {self.leverage}X")
                        self.send_system_log(f"✅ 레버리지 즉시 적용: {self.leverage}X", "SETTINGS")
                    else:
                        logger.warning(f"⚠️ 레버리지 즉시 적용 실패: {self.leverage}X")
                        self.send_system_log(f"⚠️ 레버리지 즉시 적용 실패: {self.leverage}X", "WARNING")

            # 투자 비율 업데이트
            if investment_ratio:
                new_ratio = parse_investment_ratio(investment_ratio)
                if new_ratio != self.investment_ratio:
                    self.investment_ratio = new_ratio
                    updated_fields.append('investment_ratio')

            # 헷징 임계값 업데이트
            if hedging_threshold:
                new_threshold = parse_hedging_threshold(hedging_threshold)
                if new_threshold != self.hedging_threshold:
                    self.hedging_threshold = new_threshold
                    updated_fields.append('hedging_threshold')

            if updated_fields:
                # 업데이트된 설정값 로그
                logger.info(f"🔄 설정값 동적 업데이트 완료: {updated_fields}")
                self.send_system_log(f"🔄 설정값 업데이트: {', '.join(updated_fields)}", "SETTINGS")

                # 상세 변경 내역 로그
                for field in updated_fields:
                    if field == 'symbol':
                        self.send_system_log(f"  심볼: {old_values['symbol']} → {self.symbol}", "SETTINGS")
                    elif field == 'leverage':
                        self.send_system_log(f"  레버리지: {old_values['leverage']}X → {self.leverage}X", "SETTINGS")
                    elif field == 'investment_ratio':
                        self.send_system_log(f"  투자비율: {old_values['investment_ratio']} → {self.investment_ratio*100:.0f}%", "SETTINGS")
                    elif field == 'hedging_threshold':
                        self.send_system_log(f"  헷징임계값: {old_values['hedging_threshold']} → {self.hedging_threshold:.1f}%", "SETTINGS")

                return {
                    'success': True,
                    'message': '설정값이 실시간으로 업데이트되었습니다',
                    'updated_fields': updated_fields,
                    'current_settings': {
                        'symbol': self.symbol,
                        'leverage': self.leverage,
                        'investment_ratio': f"{self.investment_ratio*100:.0f}%",
                        'hedging_threshold': f"{self.hedging_threshold:.1f}%"
                    }
                }
            else:
                return {
                    'success': False,
                    'message': '변경된 설정값이 없습니다'
                }

        except Exception as e:
            logger.error(f"설정값 동적 업데이트 오류: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def start_bot(self):
        """봇 시작 - 신호 처리 활성화"""
        try:
            self.is_running = True
            logger.info("🚀 봇 시작됨 - 신호 처리 활성화")
            self.send_system_log("🚀 봇 시작 - 신호 대기 중", "SYSTEM")
            return True
        except Exception as e:
            logger.error(f"봇 시작 오류: {e}")
            return False

    def stop_bot(self):
        """봇 중지 - 신호 처리 비활성화"""
        try:
            self.is_running = False
            logger.info("⏹️ 봇 중지됨 - 신호 처리 비활성화")
            self.send_system_log("⏹️ 봇 중지 - 신호 처리 중단", "SYSTEM")
            return True
        except Exception as e:
            logger.error(f"봇 중지 오류: {e}")
            return False

    def process_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """SignalR 신호 기반 즉시 양방향 진입"""
        try:
            # 🚨 핵심: 봇이 시작된 상태에서만 신호 처리
            if not hasattr(self, 'is_running') or not self.is_running:
                logger.warning("봇이 시작되지 않은 상태 - 신호 무시")
                return {'processed': False, 'message': '봇이 시작되지 않음 - Start Bot 버튼을 먼저 클릭하세요'}

            action = signal_data.get('action')
            signal_type = signal_data.get('signal_type')
            message = signal_data.get('message', '')

            logger.info(f"매매 신호 처리 시작 - 타입: {signal_type}, 메시지: {message}")

            # 🛡️ 로그 전용 신호는 매매 실행하지 않음
            if action == 'LOG_ONLY':
                logger.info(f"로그 전용 신호 수신 - 매매 실행 없음: {signal_type}")
                return {'processed': False, 'message': f'로그 전용 신호 ({signal_type}) - 매매 실행 없음'}

            # 🟢 SignalR 즉시 진입 신호만 처리 (단방향으로 변경)
            if action not in ['IMMEDIATE_BIDIRECTIONAL_ENTRY', 'IMMEDIATE_UNIDIRECTIONAL_ENTRY']:
                return {'processed': False, 'message': '매매 신호가 아님'}

            # 기존 포지션이 있으면 모두 청산
            if self.has_active_positions():
                logger.info("기존 포지션 청산 시작")
                self._close_all_positions()
                logger.info("기존 포지션 청산 완료")

            # 🟢 신호 방향에 따른 단방향 진입 실행
            logger.info(f"{signal_type} 신호로 단방향 진입 시작")
            result = self._enter_unidirectional_position(signal_type)

            if result.get('processed'):
                logger.info(f"{signal_type} 신호 처리 완료 - 단방향 진입 성공")
                return {
                    'processed': True,
                    'action_taken': 'IMMEDIATE_UNIDIRECTIONAL_ENTRY',
                    'signal_type': signal_type,
                    'message': f'{signal_type} 신호로 단방향 진입 완료'
                }
            else:
                logger.error(f"{signal_type} 신호 처리 실패")
                return result

        except Exception as e:
            logger.error(f"신호 처리 오류: {e}")
            return {'processed': False, 'error': str(e)}

    def has_active_positions(self) -> bool:
        """활성 포지션 존재 여부 확인"""
        return (self.current_positions['long'] is not None or
                self.current_positions['short'] is not None)

    def stop_bot_cleanup(self):
        """Stop Bot 시 모든 포지션 청산"""
        try:
            logger.info("Stop Bot 요청 - 모든 포지션 청산 시작")
            self.send_system_log("🛑 Stop Bot 요청 - 모든 포지션 청산 시작", "SYSTEM")

            if self.has_active_positions():
                logger.info("기존 포지션 청산 시작 (Stop Bot)")
                self.send_system_log("🔄 기존 포지션 청산 시작 (Stop Bot)", "SYSTEM")
                self._close_all_positions()
                logger.info("Stop Bot - 모든 포지션 청산 완료")
                self.send_system_log("✅ Stop Bot - 모든 포지션 청산 완료", "SYSTEM")
            else:
                logger.info("Stop Bot - 청산할 활성 포지션 없음")
                self.send_system_log("ℹ️ Stop Bot - 청산할 활성 포지션 없음", "SYSTEM")

        except Exception as e:
            logger.error(f"Stop Bot 포지션 청산 오류: {e}")
            self.send_system_log(f"❌ Stop Bot 포지션 청산 오류: {e}", "ERROR")



    def _close_all_positions(self):
        """모든 활성 포지션 청산 (지정가 주문 취소 + 실제 포지션 청산)"""
        try:
            # 1단계: 모든 대기 중인 지정가 주문 취소
            self._cancel_all_pending_orders()

            # 2단계: 실제 체결된 포지션 청산
            if self.current_positions['long']:
                logger.info("롱 포지션 청산 중...")
                self._close_position('long', 'STOP_BOT')

            if self.current_positions['short']:
                logger.info("숏 포지션 청산 중...")
                self._close_position('short', 'STOP_BOT')

            # 3단계: 메모리 정리 및 상태 초기화
            self.current_positions['long'] = None
            self.current_positions['short'] = None
            self.position_status = "waiting"
            self.is_hedging = False
            self.hedging_positions = {}
            self.pending_orders = {}

            logger.info("모든 포지션 및 주문 정리 완료 - 상태 초기화됨")

        except Exception as e:
            logger.error(f"포지션 청산 오류: {e}")

    def _cancel_all_pending_orders(self):
        """모든 대기 중인 지정가 주문 취소"""
        try:
            # 롱 지정가 주문 취소
            if self.current_positions['long']:
                order_id = self.current_positions['long'].get('order_id')
                if order_id:
                    logger.info(f"롱 지정가 주문 취소 중: {order_id}")
                    self.api_client.cancel_order(order_id, self.symbol)

            # 숏 지정가 주문 취소
            if self.current_positions['short']:
                order_id = self.current_positions['short'].get('order_id')
                if order_id:
                    logger.info(f"숏 지정가 주문 취소 중: {order_id}")
                    self.api_client.cancel_order(order_id, self.symbol)

            logger.info("모든 대기 중인 지정가 주문 취소 완료")

        except Exception as e:
            logger.error(f"지정가 주문 취소 오류: {e}")



    def _enter_unidirectional_position(self, signal_type: str) -> Dict[str, Any]:
        """🟢 신호 방향에 따른 단방향 포지션 진입 (+0.5% 지정가)"""
        try:
            # 🟢 1단계: 레버리지 설정 (포지션 진입 전에 반드시 실행)
            leverage_set = self.api_client.set_leverage(self.symbol, self.leverage)
            if not leverage_set:
                logger.warning(f"레버리지 설정 실패 - 기본값으로 진행: {self.leverage}X")
                self.send_system_log(f"⚠️ 레버리지 설정 실패 - 기본값으로 진행: {self.leverage}X", "WARNING")
            else:
                logger.info(f"✅ 레버리지 설정 완료: {self.leverage}X")
                self.send_system_log(f"✅ 레버리지 설정 완료: {self.leverage}X", "SYSTEM")

            # 현재 시장 가격 조회
            current_price = self._get_current_price()
            if not current_price:
                return {'processed': False, 'error': '시장 가격 조회 실패'}

            # 계좌 잔고 조회
            balance = self._get_account_balance()
            if not balance or balance <= 0:
                return {'processed': False, 'error': '잔고 부족'}

            # 🟢 포지션 크기 계산 (전체 투자 비율 사용 - 단방향이므로 분할 없음)
            total_investment = balance * self.investment_ratio
            usdt_amount = total_investment  # 전체 금액 사용

            # USDT 금액을 ETH 개수로 변환 (레버리지 적용)
            position_size = (usdt_amount * self.leverage) / current_price

            # 거래소 최대 주문 수량 제한 확인 및 자동 조정
            max_order_size = self._get_max_order_size()
            original_size = position_size

            if position_size > max_order_size:
                position_size = max_order_size
                logger.warning(f"수량 자동 조정: {original_size:.3f} ETH → {position_size:.3f} ETH (거래소 최대 주문 수량 제한 적용)")

            # 최소 수량 확인 (0.01 ETH) - 거래소 최소 주문 수량
            min_position_size = 0.01
            if position_size < min_position_size:
                position_size = min_position_size
                logger.warning(f"계산된 수량이 최소 주문 수량보다 작아 {min_position_size} ETH로 조정됨")

            # 소수점 자리 조정 (ETHUSDT는 보통 3자리까지)
            position_size = round(position_size, 3)

            logger.info(f"단방향 포지션 크기 계산 완료 - 투자비율: {self.investment_ratio*100:.0f}%, 레버리지: {self.leverage}X, 최종 수량: {position_size} ETH")

            # 🟢 신호 방향에 따른 진입 가격 설정 (+0.5% 오프셋으로 즉시 체결)
            price_offset_percentage = 0.5  # 0.5%

            if signal_type == "LONG":
                # 롱 진입: 현재가 +0.5% (즉시 체결)
                entry_price = current_price * (1 + price_offset_percentage / 100)
                side = "LONG"
                direction_text = "롱"
            elif signal_type == "SHORT":
                # 숏 진입: 현재가 -0.5% (즉시 체결)
                entry_price = current_price * (1 - price_offset_percentage / 100)
                side = "SHORT"
                direction_text = "숏"
            else:
                return {'processed': False, 'error': f'알 수 없는 신호 타입: {signal_type}'}

            logger.info(f"{direction_text} 단방향 지정가 진입 설정 - {direction_text}: ${entry_price:.2f} ({'+' if signal_type == 'LONG' else '-'}0.5%)")

            # 🟢 단일 방향 주문 실행
            order_result = self._create_limit_order(side, entry_price, position_size)

            if order_result['success']:
                # 포지션 상태 설정 (LONG/SHORT → position_status 매핑)
                if side.upper() == "LONG":
                    self.position_status = "long_only"
                elif side.upper() == "SHORT":
                    self.position_status = "short_only"
                else:
                    logger.error(f"알 수 없는 side 값: {side}")
                    self.position_status = "unknown"

                logger.info(f"{direction_text} 단방향 진입 완료 - 상태: {self.position_status}")
                self.send_system_log(f"✅ {direction_text} 단방향 진입 완료 - ${entry_price:.2f}", "TRADE")

                # 🟢 포지션 모니터링 시작 (헷징 로직 활성화)
                if not self.position_monitor_running:
                    monitoring_started = self.start_position_monitoring()
                    if monitoring_started:
                        logger.info(f"포지션 모니터링 시작됨 - 헷징 임계값: {self.hedging_threshold:.1f}%")
                        self.send_system_log(f"🔍 헷징 모니터링 시작 - 임계값: {self.hedging_threshold:.1f}%", "MONITOR")
                    else:
                        logger.error("포지션 모니터링 시작 실패")

                return {
                    'processed': True,
                    'action_taken': 'UNIDIRECTIONAL_LIMIT_ORDER',
                    'signal_type': signal_type,
                    'side': side,
                    'entry_price': entry_price,
                    'position_size': position_size,
                    'message': f'{direction_text} 단방향 진입 완료 - ${entry_price:.2f}',
                    'order': order_result['order']
                }
            else:
                return {
                    'processed': False,
                    'error': f'{direction_text} 단방향 진입 실패',
                    'order_result': order_result
                }

        except Exception as e:
            logger.error(f"단방향 포지션 진입 오류: {e}")
            return {'processed': False, 'error': str(e)}

    def _create_limit_order(self, side: str, price: float, size: float) -> Dict[str, Any]:
        """지정가 주문 생성 및 포지션 정보 저장 (헷징 모니터링을 위해)"""
        try:
            # API를 통한 지정가 주문 실행
            order_result = self.api_client.place_limit_order(
                symbol=self.symbol,
                side=side,
                size=size,
                price=price
            )

            if order_result.get('success'):
                # 🟢 포지션 정보 저장 (헷징 모니터링을 위해 필요)
                position_data = {
                    'side': side,
                    'entry_price': price,  # 지정가 주문 가격을 진입가로 설정
                    'size': size,
                    'entry_time': datetime.now(timezone.utc),
                    'order_id': order_result.get('order_id'),
                    'status': 'PENDING'  # 아직 체결되지 않은 상태
                }

                # 🟢 메모리에 포지션 정보 저장 (헷징 조건 체크를 위해)
                if side.upper() == 'LONG':
                    self.current_positions['long'] = position_data
                elif side.upper() == 'SHORT':
                    self.current_positions['short'] = position_data

                # 거래 로그 전송
                trade_direction = "[BUY ORDER]" if side == 'LONG' else "[SELL ORDER]"
                trade_message = f"{trade_direction} {size} ETH @ ${price:.2f} (예약주문 ID: {order_result.get('order_id', 'N/A')[:8]}...)"
                self._send_trade_log(trade_message)

                logger.info(f"{side} 예약주문 생성 완료: {size} ETH @ ${price:.2f} (포지션 정보 저장됨)")

                return {'success': True, 'order': position_data}
            else:
                return {'success': False, 'error': order_result.get('error')}

        except Exception as e:
            logger.error(f"예약주문 생성 오류: {e}")
            return {'success': False, 'error': str(e)}

    def check_hedging_condition(self) -> Dict[str, Any]:
        """단방향 포지션의 헷징 임계값 손실시 헷징 조건 체크"""
        try:
            # 🟢 디버깅 로그 추가
            logger.debug(f"헷징 조건 체크 시작 - 상태: {self.position_status}, 헷징중: {self.is_hedging}")
            logger.debug(f"현재 포지션 - 롱: {self.current_positions['long'] is not None}, 숏: {self.current_positions['short'] is not None}")

            if self.is_hedging:
                return {'should_hedge': False, 'reason': '이미 헷징 상태'}

            current_price = self._get_current_price()
            if not current_price:
                return {'should_hedge': False, 'error': '가격 조회 실패'}

            # 단방향 롱 포지션 체크
            if (self.position_status == "long_only" and
                self.current_positions['long'] and
                not self.current_positions['short']):

                long_pos = self.current_positions['long']
                long_pnl = ((current_price - long_pos['entry_price']) / long_pos['entry_price']) * 100

                # 🟢 상세 디버깅 로그
                logger.debug(f"롱 포지션 PnL 체크 - 현재가: ${current_price:.2f}, 진입가: ${long_pos['entry_price']:.2f}, PnL: {long_pnl:.2f}%, 임계값: {self.hedging_threshold:.1f}%")

                if long_pnl <= self.hedging_threshold:
                    logger.info(f"🔥 롱 포지션 헷징 조건 충족! PnL: {long_pnl:.2f}% <= 임계값: {self.hedging_threshold:.1f}%")
                    return {
                        'should_hedge': True,
                        'position_type': 'long',
                        'pnl': long_pnl,
                        'hedge_side': 'SHORT',
                        'current_price': current_price
                    }

            # 단방향 숏 포지션 체크
            elif (self.position_status == "short_only" and
                  self.current_positions['short'] and
                  not self.current_positions['long']):

                short_pos = self.current_positions['short']
                short_pnl = ((short_pos['entry_price'] - current_price) / short_pos['entry_price']) * 100

                # 🟢 상세 디버깅 로그
                logger.debug(f"숏 포지션 PnL 체크 - 현재가: ${current_price:.2f}, 진입가: ${short_pos['entry_price']:.2f}, PnL: {short_pnl:.2f}%, 임계값: {self.hedging_threshold:.1f}%")

                if short_pnl <= self.hedging_threshold:
                    logger.info(f"🔥 숏 포지션 헷징 조건 충족! PnL: {short_pnl:.2f}% <= 임계값: {self.hedging_threshold:.1f}%")
                    return {
                        'should_hedge': True,
                        'position_type': 'short',
                        'pnl': short_pnl,
                        'hedge_side': 'LONG',
                        'current_price': current_price
                    }

            return {'should_hedge': False, 'reason': '헷징 조건 미충족'}

        except Exception as e:
            logger.error(f"헷징 조건 체크 오류: {e}")
            return {'should_hedge': False, 'error': str(e)}

    def execute_hedging(self, hedge_info: Dict[str, Any]) -> Dict[str, Any]:
        """헷징 포지션 실행"""
        try:
            original_position_type = hedge_info['position_type']
            hedge_side = hedge_info['hedge_side']
            current_price = hedge_info['current_price']

            # 원본 포지션 크기와 동일한 크기로 헷징
            original_position = self.current_positions[original_position_type]
            hedge_size = original_position['size']

            logger.info(f"헷징 실행 시작 - 원본: {original_position_type}, 헷징: {hedge_side}, 크기: {hedge_size}")
            self.send_system_log(f"🔄 헷징 실행 - {original_position_type} 포지션 손실로 인한 {hedge_side} 헷징", "HEDGE")

            # 헷징 포지션 생성 (시장가)
            hedge_result = self._create_position(hedge_side, current_price, hedge_size)

            if hedge_result.get('success'):
                # 헷징 상태로 전환
                self.is_hedging = True
                if original_position_type == 'long':
                    self.position_status = "hedging_long_short"
                else:
                    self.position_status = "hedging_short_long"

                # 헷징 포지션 정보 저장
                self.hedging_positions = {
                    'original': original_position,
                    'hedge': hedge_result['position'],
                    'hedge_trigger_price': current_price,
                    'hedge_time': datetime.now(timezone.utc)
                }

                logger.info(f"헷징 완료 - 상태: {self.position_status}")
                self.send_system_log(f"✅ 헷징 완료 - {self.position_status}", "HEDGE")

                return {
                    'success': True,
                    'action': 'HEDGING_EXECUTED',
                    'original_position': original_position_type,
                    'hedge_side': hedge_side,
                    'hedge_result': hedge_result
                }
            else:
                logger.error(f"헷징 실행 실패: {hedge_result.get('error')}")
                return {'success': False, 'error': f'헷징 실행 실패: {hedge_result.get("error")}'}

        except Exception as e:
            logger.error(f"헷징 실행 오류: {e}")
            return {'success': False, 'error': str(e)}

    def _check_stop_loss(self) -> Dict[str, Any]:
        """손실 감지 (-0.5%)"""
        try:
            current_price = self._get_current_price()
            if not current_price:
                return {'should_close': False, 'error': '가격 조회 실패'}

            total_pnl = 0
            positions_to_close = []

            # 롱 포지션 PnL 계산
            if self.current_positions['long']:
                long_pos = self.current_positions['long']
                long_pnl = ((current_price - long_pos['entry_price']) / long_pos['entry_price']) * 100
                total_pnl += long_pnl

                if long_pnl <= self.stop_loss_percentage:
                    positions_to_close.append(('long', long_pnl))

            # 숏 포지션 PnL 계산
            if self.current_positions['short']:
                short_pos = self.current_positions['short']
                short_pnl = ((short_pos['entry_price'] - current_price) / short_pos['entry_price']) * 100
                total_pnl += short_pnl

                if short_pnl <= self.stop_loss_percentage:
                    positions_to_close.append(('short', short_pnl))

            return {
                'should_close': len(positions_to_close) > 0,
                'total_pnl': total_pnl,
                'positions_to_close': positions_to_close,
                'current_price': current_price
            }

        except Exception as e:
            logger.error(f"손실 감지 오류: {e}")
            return {'should_close': False, 'error': str(e)}

    def _handle_stop_loss(self, pnl_check: Dict[str, Any]) -> Dict[str, Any]:
        """손실 감지 시 처리 및 방향 전환"""
        try:
            positions_to_close = pnl_check.get('positions_to_close', [])
            results = []

            for position_type, pnl in positions_to_close:
                # 손실 포지션 종료
                close_result = self._close_position(position_type, 'STOP_LOSS')
                results.append({
                    'position_type': position_type,
                    'pnl': pnl,
                    'close_result': close_result
                })

                # 반대 방향으로 포지션 전환
                opposite_direction = 'SHORT' if position_type == 'long' else 'LONG'
                flip_result = self._flip_position(position_type, opposite_direction)
                results.append({
                    'action': 'FLIP',
                    'from': position_type,
                    'to': opposite_direction,
                    'result': flip_result
                })

            return {
                'processed': True,
                'action_taken': 'STOP_LOSS_AND_FLIP',
                'message': f'손실 감지 및 방향 전환 완료',
                'results': results
            }

        except Exception as e:
            logger.error(f"손실 처리 오류: {e}")
            return {'processed': False, 'error': str(e)}

    def _handle_direction_change(self, trading_signal: Dict[str, Any]) -> Dict[str, Any]:
        """새로운 신호에 따른 방향 전환"""
        try:
            action = trading_signal.get('action')

            if action == 'PREPARE_LONG':
                # 숏 포지션이 있으면 롱으로 전환
                if self.current_positions['short']:
                    flip_result = self._flip_position('short', 'LONG')
                    return {
                        'processed': True,
                        'action_taken': 'DIRECTION_CHANGE_TO_LONG',
                        'message': '숏에서 롱으로 방향 전환',
                        'result': flip_result
                    }

            elif action == 'PREPARE_SHORT':
                # 롱 포지션이 있으면 숏으로 전환
                if self.current_positions['long']:
                    flip_result = self._flip_position('long', 'SHORT')
                    return {
                        'processed': True,
                        'action_taken': 'DIRECTION_CHANGE_TO_SHORT',
                        'message': '롱에서 숏으로 방향 전환',
                        'result': flip_result
                    }

            return {'processed': False, 'message': '방향 전환 조건 불충족'}

        except Exception as e:
            logger.error(f"방향 전환 오류: {e}")
            return {'processed': False, 'error': str(e)}

    def _create_position(self, side: str, price: float, size: float) -> Dict[str, Any]:
        """포지션 생성"""
        try:
            # API를 통한 시장가 주문 실행
            order_result = self.api_client.place_market_order(
                symbol=self.symbol,
                side=side,
                size=size
            )

            if order_result.get('success'):
                # 포지션 정보 저장
                position_data = {
                    'side': side,
                    'entry_price': price,
                    'size': size,
                    'entry_time': datetime.now(timezone.utc),
                    'order_id': order_result.get('order_id')
                }

                # 메모리에 저장
                if side == 'LONG':
                    self.current_positions['long'] = position_data
                else:
                    self.current_positions['short'] = position_data

                # 데이터베이스에 저장
                position = Position(
                    user_id=self.user_id,
                    symbol=self.symbol,
                    position_type=side,
                    entry_price=price,
                    size=size
                )
                db_session.add(position)
                db_session.commit()

                return {'success': True, 'position': position_data}
            else:
                return {'success': False, 'error': order_result.get('error')}

        except Exception as e:
            logger.error(f"포지션 생성 오류: {e}")
            return {'success': False, 'error': str(e)}

    def _create_limit_position(self, side: str, price: float, size: float) -> Dict[str, Any]:
        """포지션 생성 (지정가)"""
        try:
            # API를 통한 지정가 주문 실행
            order_result = self.api_client.place_limit_order(
                symbol=self.symbol,
                side=side,
                size=size,
                price=price
            )

            if order_result.get('success'):
                # 거래 로그 전송 (SignalR을 통해 프론트엔드로) - DB 저장 전에 먼저 실행
                trade_direction = "[BUY]" if side == 'LONG' else "[SELL]"
                trade_message = f"{trade_direction} {size} ETH @ ${price:.2f} (주문ID: {order_result.get('order_id', 'N/A')[:8]}...)"
                self._send_trade_log(trade_message)

                # 포지션 정보 저장
                position_data = {
                    'side': side,
                    'entry_price': price,
                    'size': size,
                    'entry_time': datetime.now(timezone.utc),
                    'order_id': order_result.get('order_id')
                }

                # 메모리에 저장
                if side == 'LONG':
                    self.current_positions['long'] = position_data
                else:
                    self.current_positions['short'] = position_data

                # 데이터베이스에 저장 (오류가 발생해도 거래 로그는 이미 전송됨)
                try:
                    position = Position(
                        user_id=self.user_id,
                        symbol=self.symbol,
                        position_type=side,
                        entry_price=price,
                        size=size
                    )
                    db_session.add(position)
                    db_session.commit()
                except Exception as db_error:
                    logger.error(f"DB 저장 오류 (거래 로그는 전송됨): {db_error}")

                return {'success': True, 'position': position_data}
            else:
                return {'success': False, 'error': order_result.get('error')}

        except Exception as e:
            logger.error(f"지정가 포지션 생성 오류: {e}")
            return {'success': False, 'error': str(e)}

    def _close_position(self, position_type: str, reason: str) -> Dict[str, Any]:
        """포지션 종료"""
        try:
            position = self.current_positions.get(position_type)
            if not position:
                return {'success': False, 'error': '포지션이 존재하지 않음'}

            current_price = self._get_current_price()

            # API를 통한 포지션 종료
            close_result = self.api_client.close_position_by_side(
                symbol=self.symbol,
                side='LONG' if position_type == 'long' else 'SHORT',
                size=position['size']
            )

            if close_result.get('success'):
                # 포지션 정리
                self.current_positions[position_type] = None

                # 거래 기록 저장
                trade = Trade(
                    user_id=self.user_id,
                    symbol=self.symbol,
                    side='SELL' if position_type == 'long' else 'BUY',
                    price=current_price,
                    size=position['size'],
                    trade_type='EXIT',
                    reason=reason
                )
                db_session.add(trade)
                db_session.commit()

                return {'success': True, 'trade': trade}
            else:
                return {'success': False, 'error': close_result.get('error')}

        except Exception as e:
            logger.error(f"포지션 종료 오류: {e}")
            return {'success': False, 'error': str(e)}

    def _flip_position(self, from_position: str, to_direction: str) -> Dict[str, Any]:
        """포지션 방향 전환"""
        try:
            # 기존 포지션 종료
            close_result = self._close_position(from_position, 'DIRECTION_CHANGE')

            if close_result.get('success'):
                # 새로운 방향으로 포지션 생성
                current_price = self._get_current_price()
                balance = self._get_account_balance()
                total_investment = balance * self.investment_ratio
                usdt_amount = total_investment * self.position_split_ratio
                position_size = (usdt_amount * self.leverage) / current_price

                # 거래소 최대 주문 수량 제한 확인 및 자동 조정
                max_order_size = self._get_max_order_size()
                original_size = position_size

                if position_size > max_order_size:
                    position_size = max_order_size
                    logger.warning(f"방향 전환 시 수량 자동 조정: {original_size:.3f} ETH → {position_size:.3f} ETH (거래소 제한 적용)")

                # 최소 수량 확인
                min_position_size = 0.01
                if position_size < min_position_size:
                    position_size = min_position_size
                    logger.warning(f"방향 전환 시 계산된 수량이 최소 주문 수량보다 작아 {min_position_size} ETH로 조정됨")

                position_size = round(position_size, 3)
                logger.info(f"방향 전환 포지션 크기: {position_size} ETH")

                create_result = self._create_position(to_direction, current_price, position_size)

                return {
                    'success': create_result.get('success'),
                    'close_result': close_result,
                    'create_result': create_result
                }
            else:
                return {'success': False, 'error': '기존 포지션 종료 실패'}

        except Exception as e:
            logger.error(f"포지션 전환 오류: {e}")
            return {'success': False, 'error': str(e)}

    def _get_current_price(self) -> Optional[float]:
        """현재 시장 가격 조회"""
        try:
            price_data = self.api_client.get_ticker(self.symbol)
            return price_data.get('price') if price_data else None
        except Exception as e:
            logger.error(f"가격 조회 오류: {e}")
            return None

    def _get_account_balance(self) -> Optional[float]:
        """계좌 잔고 조회"""
        try:
            balance_data = self.api_client.get_balance()
            return balance_data.get('available_balance') if balance_data else None
        except Exception as e:
            logger.error(f"잔고 조회 오류: {e}")
            return None

    def _get_max_order_size(self) -> float:
        """심볼별 최대 주문 수량 조회"""
        # Bybit ETHUSDT 최대 주문 수량 (시장가 주문 기준)
        # 실제 테스트 결과에 따라 더 보수적으로 설정
        max_order_limits = {
            'ETHUSDT': 5.0,   # ETHUSDT 최대 주문 수량 (보수적 설정)
            'BTCUSDT': 10.0,  # BTCUSDT 최대 주문 수량 (참고용)
            'default': 1.0    # 기본값 (알 수 없는 심볼)
        }

        return max_order_limits.get(self.symbol, max_order_limits['default'])

    def _check_order_fills_and_cancel_opposite(self):
        """예약주문 체결 확인 및 반대 주문 취소"""
        try:
            # 예약주문 대기 상태가 아니면 리턴
            if self.position_status != "waiting_for_fill" or not self.pending_orders:
                return

            # 거래소에서 실제 포지션 조회
            actual_positions = self.api_client.get_positions(self.symbol)
            if not actual_positions:
                return

            # 응답 형식 처리
            if isinstance(actual_positions, dict):
                if not actual_positions.get('success'):
                    return
                positions_data = actual_positions.get('positions', [])
            elif isinstance(actual_positions, list):
                positions_data = actual_positions
            else:
                return

            # 실제 체결된 포지션 확인
            actual_long_filled = False
            actual_short_filled = False
            filled_long_data = None
            filled_short_data = None

            for pos in positions_data:
                if float(pos.get('size', 0)) > 0:
                    side = pos.get('side', '').lower()
                    if side == 'buy':
                        actual_long_filled = True
                        filled_long_data = {
                            'side': 'LONG',
                            'entry_price': float(pos.get('avgPrice', 0)),
                            'size': float(pos.get('size', 0)),
                            'entry_time': datetime.now(timezone.utc),
                            'order_id': self.pending_orders.get('long', {}).get('order_id')
                        }
                    elif side == 'sell':
                        actual_short_filled = True
                        filled_short_data = {
                            'side': 'SHORT',
                            'entry_price': float(pos.get('avgPrice', 0)),
                            'size': float(pos.get('size', 0)),
                            'entry_time': datetime.now(timezone.utc),
                            'order_id': self.pending_orders.get('short', {}).get('order_id')
                        }

            # 한쪽만 체결된 경우 포지션 생성 및 반대편 주문 취소
            if actual_long_filled and not actual_short_filled:
                # 🟢 롱 포지션 체결됨 - 상태를 FILLED로 업데이트
                filled_long_data['status'] = 'FILLED'  # 체결 상태로 변경
                self.current_positions['long'] = filled_long_data
                self.current_positions['short'] = None
                self.position_status = "long_only"
                self.is_hedging = False

                # 숏 예약주문 취소
                if 'short' in self.pending_orders:
                    short_order_id = self.pending_orders['short'].get('order_id')
                    if short_order_id:
                        self.api_client.cancel_order(short_order_id, self.symbol)

                self.pending_orders = {}
                self._send_trade_log(f"🚀 상승 돌파! 롱 포지션 체결 완료 - 숏 예약주문 취소")
                self.send_system_log(f"✅ 롱 포지션: {filled_long_data['size']} ETH @ ${filled_long_data['entry_price']:.2f} (단방향 운영 시작)", "TRADE")
                logger.info(f"상승 돌파 성공 - 롱 포지션 체결: {filled_long_data['size']} ETH @ ${filled_long_data['entry_price']:.2f}")

                # 🟢 헷징 모니터링 시작 알림
                logger.info(f"🔍 헷징 모니터링 활성화 - 임계값: {self.hedging_threshold:.1f}%")
                self.send_system_log(f"🔍 헷징 모니터링 활성화 - 임계값: {self.hedging_threshold:.1f}%", "MONITOR")

            elif actual_short_filled and not actual_long_filled:
                # 🟢 숏 포지션 체결됨 - 상태를 FILLED로 업데이트
                filled_short_data['status'] = 'FILLED'  # 체결 상태로 변경
                self.current_positions['short'] = filled_short_data
                self.current_positions['long'] = None
                self.position_status = "short_only"
                self.is_hedging = False

                # 롱 예약주문 취소
                if 'long' in self.pending_orders:
                    long_order_id = self.pending_orders['long'].get('order_id')
                    if long_order_id:
                        self.api_client.cancel_order(long_order_id, self.symbol)

                self.pending_orders = {}
                self._send_trade_log(f"📉 하락 돌파! 숏 포지션 체결 완료 - 롱 예약주문 취소")
                self.send_system_log(f"✅ 숏 포지션: {filled_short_data['size']} ETH @ ${filled_short_data['entry_price']:.2f} (단방향 운영 시작)", "TRADE")
                logger.info(f"하락 돌파 성공 - 숏 포지션 체결: {filled_short_data['size']} ETH @ ${filled_short_data['entry_price']:.2f}")

                # 🟢 헷징 모니터링 시작 알림
                logger.info(f"🔍 헷징 모니터링 활성화 - 임계값: {self.hedging_threshold:.1f}%")
                self.send_system_log(f"🔍 헷징 모니터링 활성화 - 임계값: {self.hedging_threshold:.1f}%", "MONITOR")

        except Exception as e:
            logger.error(f"예약주문 체결 확인 오류: {e}")

    def _cancel_pending_order(self, position_type: str):
        """대기 중인 지정가 주문 취소"""
        try:
            position = self.current_positions.get(position_type)
            if not position:
                return

            order_id = position.get('order_id')
            if not order_id:
                return

            # API를 통한 주문 취소 (order_id, symbol 순서)
            cancel_result = self.api_client.cancel_order(order_id, self.symbol)

            if cancel_result:
                logger.info(f"{position_type} 지정가 주문 취소 성공: {order_id}")
                return True
            else:
                logger.warning(f"{position_type} 지정가 주문 취소 실패: {order_id}")
                return False

        except Exception as e:
            logger.error(f"주문 취소 오류: {e}")
            return False

    def _send_trade_log(self, message: str):
        """거래 로그를 SignalR을 통해 프론트엔드로 전송"""
        try:
            # 🟢 1단계: 파일 저장 (새로운 기능 추가)
            self._save_log_to_file(message, "TRADE")
            # SignalProcessor와 SignalR 클라이언트 존재 확인
            if not self.signal_processor:
                logger.warning("SignalProcessor가 없음 - 거래 로그 전송 실패")
                return

            if not hasattr(self.signal_processor, 'signalr_client'):
                logger.warning("SignalR 클라이언트가 없음 - 거래 로그 전송 실패")
                return

            signalr_client = self.signal_processor.signalr_client
            if not signalr_client:
                logger.warning("SignalR 클라이언트가 None - 거래 로그 전송 실패")
                return

            # 연결 상태 확인 및 전송
            logger.info(f"SignalR 연결 상태: {signalr_client.is_connected}")

            if signalr_client.is_connected:
                # 기존 신호와 동일한 방식으로 전송 (SendSignal 사용)
                try:
                    # 거래 로그를 신호 형태로 전송
                    trade_signal = f"[TRADE] {message}"
                    result = signalr_client.send_message("SendSignal", trade_signal)
                    if result:
                        logger.info(f"거래 로그 전송 성공 (SendSignal 방식): {trade_signal}")
                    else:
                        logger.warning(f"거래 로그 전송 실패 (SendSignal 방식): {trade_signal}")
                except Exception as send_error:
                    logger.error(f"거래 로그 전송 오류 (SendSignal 방식): {send_error}")
            else:
                logger.warning(f"SignalR 연결되지 않음 - 거래 로그 전송 실패: {message}")

        except Exception as e:
            logger.error(f"거래 로그 전송 오류: {e}")

    def send_system_log(self, message: str, log_type: str = "SYSTEM"):
        """시스템 로그를 SignalR을 통해 전송"""
        try:
            # 🟢 1단계: 파일 저장 (새로운 기능 추가)
            self._save_log_to_file(message, log_type)
            # SignalProcessor와 SignalR 클라이언트 존재 확인
            if not self.signal_processor:
                logger.warning("SignalProcessor가 없음 - 시스템 로그 전송 실패")
                return

            if not hasattr(self.signal_processor, 'signalr_client'):
                logger.warning("SignalR 클라이언트가 없음 - 시스템 로그 전송 실패")
                return

            signalr_client = self.signal_processor.signalr_client
            if not signalr_client:
                logger.warning("SignalR 클라이언트가 None - 시스템 로그 전송 실패")
                return

            if signalr_client.is_connected:
                try:
                    # 시스템 로그를 신호 형태로 전송
                    system_signal = f"[{log_type}] {message}"
                    result = signalr_client.send_message("SendSignal", system_signal)
                    if result:
                        logger.info(f"시스템 로그 전송 성공: {system_signal}")
                    else:
                        logger.warning(f"시스템 로그 전송 실패: {system_signal}")
                except Exception as send_error:
                    logger.error(f"시스템 로그 전송 오류: {send_error}")
            else:
                logger.warning(f"SignalR 연결되지 않음 - 시스템 로그 전송 실패: {message}")

        except Exception as e:
            logger.error(f"시스템 로그 전송 오류: {e}")

    def _save_log_to_file(self, message: str, log_type: str):
        """로그를 파일에 저장 - 기존 SignalR 로직과 완전 분리"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_line = f"{timestamp}  [{log_type}] {message}\n"

            # 일별 로그 파일 생성
            log_file = f"logs/system_trade_{datetime.now().strftime('%Y%m%d')}.log"

            # 로그 디렉토리 확인 및 생성
            import os
            log_dir = os.path.dirname(log_file)
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 파일에 추가 저장
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_line)

        except Exception as e:
            # 파일 저장 실패해도 기존 SignalR 로직에 영향 없음
            logger.debug(f"로그 파일 저장 실패 (SignalR은 정상): {e}")

    def start_position_monitoring(self):
        """포지션 모니터링 스레드 시작"""
        try:
            if self.position_monitor_running:
                logger.warning("포지션 모니터링이 이미 실행 중입니다")
                return False

            self.position_monitor_running = True
            self.position_monitor_stop_event.clear()

            self.position_monitor_thread = threading.Thread(
                target=self._position_monitor_loop,
                daemon=True,
                name="PositionMonitor"
            )
            self.position_monitor_thread.start()

            logger.info("포지션 모니터링 스레드 시작됨")
            return True

        except Exception as e:
            logger.error(f"포지션 모니터링 시작 오류: {e}")
            return False

    def stop_position_monitoring(self):
        """포지션 모니터링 스레드 중지"""
        try:
            if not self.position_monitor_running:
                return

            self.position_monitor_running = False
            self.position_monitor_stop_event.set()

            if self.position_monitor_thread and self.position_monitor_thread.is_alive():
                self.position_monitor_thread.join(timeout=5)

            logger.info("포지션 모니터링 스레드 중지됨")

        except Exception as e:
            logger.error(f"포지션 모니터링 중지 오류: {e}")

    def _position_monitor_loop(self):
        """백그라운드 포지션 모니터링 루프"""
        logger.info("포지션 모니터링 루프 시작")

        while self.position_monitor_running and not self.position_monitor_stop_event.is_set():
            try:
                # 포지션 변화 확인
                self._check_position_changes()

                # 양방향 주문 체결 확인 및 반대 주문 취소
                self._check_order_fills_and_cancel_opposite()

                # 🟢 헷징 조건 체크 (단방향 포지션인 경우) - 더 상세한 로그 추가
                if not self.is_hedging and self.position_status in ["long_only", "short_only"]:
                    logger.debug(f"헷징 조건 체크 중 - 상태: {self.position_status}")
                    hedge_check = self.check_hedging_condition()

                    if hedge_check.get('should_hedge'):
                        logger.info(f"🚨 헷징 조건 충족! - PnL: {hedge_check.get('pnl'):.2f}%, 임계값: {self.hedging_threshold:.1f}%")
                        self.send_system_log(f"🚨 헷징 조건 충족 - PnL: {hedge_check.get('pnl'):.2f}%", "HEDGE")

                        hedge_result = self.execute_hedging(hedge_check)
                        if hedge_result.get('success'):
                            logger.info("✅ 헷징 실행 성공")
                            self.send_system_log("✅ 헷징 실행 성공", "HEDGE")
                        else:
                            logger.error(f"❌ 헷징 실행 실패: {hedge_result.get('error')}")
                            self.send_system_log(f"❌ 헷징 실행 실패: {hedge_result.get('error')}", "ERROR")
                    else:
                        # 🟢 헷징 조건 미충족 시에도 주기적으로 상태 로그 (5분마다)
                        import time
                        current_time = time.time()
                        if not hasattr(self, '_last_hedge_check_log') or (current_time - self._last_hedge_check_log) > 300:  # 5분
                            logger.debug(f"헷징 조건 미충족 - 이유: {hedge_check.get('reason', 'N/A')}")
                            self._last_hedge_check_log = current_time

                # 🟢 5초 대기 (헷징 조건을 더 자주 체크)
                if self.position_monitor_stop_event.wait(timeout=5):
                    break

            except Exception as e:
                logger.error(f"포지션 모니터링 루프 오류: {e}")
                # 오류 발생 시 5초 대기 후 재시도
                if self.position_monitor_stop_event.wait(timeout=5):
                    break

        logger.info("포지션 모니터링 루프 종료")

    def _check_position_changes(self):
        """포지션 변화 확인 및 동기화"""
        try:
            # 바이비트에서 실제 포지션 조회
            actual_positions = self.api_client.get_positions(self.symbol)

            # API 응답 형식 확인 및 처리
            if not actual_positions:
                return  # 조회 실패 시 조용히 넘어감

            # 응답이 dict 형태인 경우
            if isinstance(actual_positions, dict):
                if not actual_positions.get('success'):
                    return
                positions_data = actual_positions.get('positions', [])
            # 응답이 list 형태인 경우 (직접 포지션 데이터)
            elif isinstance(actual_positions, list):
                positions_data = actual_positions
            else:
                return  # 예상하지 못한 형식

            # 현재 시스템 포지션과 실제 포지션 비교
            changes_detected = self._sync_positions_with_exchange(positions_data)

            # 변화가 감지된 경우에만 로그 전송
            if changes_detected:
                self._send_trade_log("포지션 동기화: 바이비트 포지션 변화 감지 및 업데이트 완료")

        except Exception as e:
            logger.error(f"포지션 변화 확인 오류: {e}")

    def _sync_positions_with_exchange(self, exchange_positions) -> bool:
        """거래소 실제 포지션과 시스템 동기화"""
        try:
            changes_detected = False

            # 거래소 포지션 파싱
            actual_long = None
            actual_short = None

            for pos in exchange_positions:
                if float(pos.get('size', 0)) > 0:  # 포지션이 존재하는 경우
                    side = pos.get('side', '').lower()
                    position_data = {
                        'side': 'LONG' if side == 'buy' else 'SHORT',
                        'entry_price': float(pos.get('avgPrice', 0)),
                        'size': float(pos.get('size', 0)),
                        'unrealised_pnl': float(pos.get('unrealisedPnl', 0))
                    }

                    if side == 'buy':
                        actual_long = position_data
                    elif side == 'sell':
                        actual_short = position_data

            # 시스템 포지션과 비교 및 동기화
            # 롱 포지션 확인
            if self.current_positions['long'] != actual_long:
                if actual_long is None and self.current_positions['long'] is not None:
                    # 롱 포지션이 수동으로 청산됨
                    self._send_trade_log("포지션 동기화: 롱 포지션 수동 청산 감지됨")
                    changes_detected = True
                elif actual_long is not None and self.current_positions['long'] is None:
                    # 새로운 롱 포지션이 수동으로 생성됨
                    self._send_trade_log(f"포지션 동기화: 새로운 롱 포지션 감지 - {actual_long['size']} ETH @ ${actual_long['entry_price']:.2f}")
                    changes_detected = True

                self.current_positions['long'] = actual_long

            # 숏 포지션 확인
            if self.current_positions['short'] != actual_short:
                if actual_short is None and self.current_positions['short'] is not None:
                    # 숏 포지션이 수동으로 청산됨
                    self._send_trade_log("포지션 동기화: 숏 포지션 수동 청산 감지됨")
                    changes_detected = True
                elif actual_short is not None and self.current_positions['short'] is None:
                    # 새로운 숏 포지션이 수동으로 생성됨
                    self._send_trade_log(f"포지션 동기화: 새로운 숏 포지션 감지 - {actual_short['size']} ETH @ ${actual_short['entry_price']:.2f}")
                    changes_detected = True

                self.current_positions['short'] = actual_short

            return changes_detected

        except Exception as e:
            logger.error(f"포지션 동기화 오류: {e}")
            return False

    def _initialize_profit_tracking(self):
        """Total Profit 추적 초기화"""
        try:
            from models.profit_tracking import ProfitTracking
            from database import db_session

            # 기존 활성 profit tracking 조회
            existing_tracking = db_session.query(ProfitTracking).filter_by(
                user_id=self.user_id,
                is_active=True
            ).first()

            if existing_tracking:
                # 기존 추적 데이터 사용
                self.profit_tracking = existing_tracking
                self.profit_tracking_initialized = True
                logger.info(f"기존 Total Profit 추적 로드: 기준 자산 ${existing_tracking.initial_equity:.2f}")
                return True

            # 새로운 추적 데이터 생성
            current_balance = self._get_current_balance()
            if current_balance > 0:
                # 현재 포지션 PnL 계산
                position_pnl = self._calculate_current_position_pnl()
                current_equity = current_balance + position_pnl

                # 새로운 ProfitTracking 생성
                new_tracking = ProfitTracking(
                    user_id=self.user_id,
                    initial_balance=current_balance,
                    initial_equity=current_equity,
                    reset_reason="Initial Setup"
                )

                db_session.add(new_tracking)
                db_session.commit()

                self.profit_tracking = new_tracking
                self.profit_tracking_initialized = True

                logger.info(f"새로운 Total Profit 추적 생성: 기준 자산 ${current_equity:.2f}")
                return True
            else:
                logger.warning("현재 잔고를 조회할 수 없어 Total Profit 추적을 초기화하지 못했습니다")
                return False

        except Exception as e:
            logger.error(f"Total Profit 추적 초기화 오류: {e}")
            self.profit_tracking_initialized = False
            return False

    def _get_current_balance(self):
        """현재 계좌 잔고 조회"""
        try:
            balance_result = self.api_client.get_balance()
            if balance_result and isinstance(balance_result, dict):
                return balance_result.get('available_balance', 0.0)
            return 0.0
        except Exception as e:
            logger.error(f"잔고 조회 오류: {e}")
            return 0.0

    def _calculate_current_position_pnl(self):
        """현재 포지션 평가손익 계산"""
        try:
            total_pnl = 0.0
            current_price = self._get_current_price()

            if not current_price:
                return 0.0

            # 롱 포지션 PnL
            if self.current_positions['long']:
                long_pos = self.current_positions['long']
                long_pnl = (current_price - long_pos['entry_price']) * long_pos['size']
                total_pnl += long_pnl

            # 숏 포지션 PnL
            if self.current_positions['short']:
                short_pos = self.current_positions['short']
                short_pnl = (short_pos['entry_price'] - current_price) * short_pos['size']
                total_pnl += short_pnl

            return total_pnl

        except Exception as e:
            logger.error(f"포지션 PnL 계산 오류: {e}")
            return 0.0

    def calculate_total_profit(self):
        """Total Profit 계산 (백분율)"""
        try:
            if not self.profit_tracking_initialized or not self.profit_tracking:
                return 0.0

            # 현재 총자산 계산
            current_balance = self._get_current_balance()
            position_pnl = self._calculate_current_position_pnl()
            current_equity = current_balance + position_pnl

            # 수익률 계산 (직접 계산)
            if self.profit_tracking.initial_equity <= 0:
                return 0.0

            profit_percentage = ((current_equity - self.profit_tracking.initial_equity) / self.profit_tracking.initial_equity) * 100
            return round(profit_percentage, 2)

        except Exception as e:
            logger.error(f"Total Profit 계산 오류: {e}")
            return 0.0

    def reset_profit_tracking(self, reason="Manual Reset"):
        """Total Profit 기준점 리셋"""
        try:
            if not self.profit_tracking:
                return self._initialize_profit_tracking()

            # 현재 자산 계산
            current_balance = self._get_current_balance()
            position_pnl = self._calculate_current_position_pnl()
            current_equity = current_balance + position_pnl

            if current_equity > 0:
                from database import db_session

                # 기존 추적 데이터 리셋
                self.profit_tracking.reset_tracking(current_balance, current_equity, reason)
                db_session.commit()

                logger.info(f"Total Profit 리셋 완료: 새로운 기준 자산 ${current_equity:.2f} (사유: {reason})")
                return True
            else:
                logger.error("현재 자산을 계산할 수 없어 리셋하지 못했습니다")
                return False

        except Exception as e:
            logger.error(f"Total Profit 리셋 오류: {e}")
            return False

    def _save_signal_result(self, signal_data: Dict[str, Any], result: Dict[str, Any]):
        """신호 처리 결과 저장"""
        try:
            signal = Signal(
                user_id=self.user_id,
                symbol=self.symbol,
                data=signal_data
            )
            signal.set_result(result)

            db_session.add(signal)
            db_session.commit()

        except Exception as e:
            logger.error(f"신호 결과 저장 오류: {e}")

    def get_status(self) -> Dict[str, Any]:
        """현재 봇 상태 조회"""
        return {
            'current_positions': self.current_positions,
            'symbol': self.symbol,
            'has_active_positions': self.has_active_positions(),
            'status_text': "SignalR 신호 대기 중" if not self.has_active_positions() else "포지션 활성"
        }
