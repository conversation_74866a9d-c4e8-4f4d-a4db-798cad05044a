#!/usr/bin/env python3
# 데이터베이스 분석 스크립트
import sqlite3
import os

def analyze_database():
    """기존 데이터베이스 구조 분석"""
    db_path = "db/opensystems_bot.db"
    
    if not os.path.exists(db_path):
        print("❌ 데이터베이스 파일이 존재하지 않습니다.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 데이터베이스 분석 시작...")
        print("=" * 50)
        
        # 1. 모든 테이블 목록 조회
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📋 테이블 목록:")
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\n" + "=" * 50)
        
        # 2. settings 테이블 구조 확인
        print("🎯 settings 테이블 구조:")
        cursor.execute("PRAGMA table_info(settings);")
        columns = cursor.fetchall()
        
        if columns:
            print("  현재 컬럼들:")
            for col in columns:
                print(f"    - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT: ' + str(col[4]) if col[4] else ''}")
        else:
            print("  ❌ settings 테이블이 존재하지 않습니다.")
        
        # 3. settings 테이블 데이터 확인
        print("\n📊 settings 테이블 데이터:")
        try:
            cursor.execute("SELECT * FROM settings;")
            rows = cursor.fetchall()
            
            if rows:
                print(f"  총 {len(rows)}개의 설정 레코드:")
                for i, row in enumerate(rows):
                    print(f"    레코드 {i+1}: {row}")
            else:
                print("  📝 설정 데이터가 없습니다.")
        except Exception as e:
            print(f"  ❌ 데이터 조회 오류: {e}")
        
        # 4. hedging_threshold 컬럼 존재 여부 확인
        print("\n🔍 hedging_threshold 컬럼 확인:")
        hedging_column_exists = any(col[1] == 'hedging_threshold' for col in columns)
        
        if hedging_column_exists:
            print("  ✅ hedging_threshold 컬럼이 존재합니다.")
        else:
            print("  ❌ hedging_threshold 컬럼이 존재하지 않습니다.")
            print("  🔧 ALTER TABLE로 컬럼 추가가 필요합니다.")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("🎯 분석 완료!")
        
        return hedging_column_exists
        
    except Exception as e:
        print(f"❌ 데이터베이스 분석 오류: {e}")
        return False

if __name__ == "__main__":
    analyze_database()
