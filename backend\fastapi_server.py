# FastAPI WebSocket 서버
"""
FastAPI 기반 WebSocket 서버
Flutter와 실시간 통신을 위한 구현
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Set
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn

# 내부 모듈
from database import init_db
from config import Config
from utils.logger import setup_logger

# 로거 설정
logger = setup_logger("fastapi_websocket")

# FastAPI 앱 초기화
app = FastAPI(title="OPENSYSTEMS BOT WebSocket API", version="1.0.0")

# CORS 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 개발용 - 프로덕션에서는 제한 필요
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket 연결 관리
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[int, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str, user_id: int = None):
        """클라이언트 연결"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(client_id)
            
        logger.info(f"WebSocket 연결: {client_id} (사용자: {user_id})")
        
    def disconnect(self, client_id: str, user_id: int = None):
        """클라이언트 연결 해제"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(client_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
                
        logger.info(f"WebSocket 연결 해제: {client_id} (사용자: {user_id})")
        
    async def send_personal_message(self, message: dict, client_id: str):
        """개별 메시지 전송"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"메시지 전송 실패 ({client_id}): {e}")
                
    async def send_user_message(self, message: dict, user_id: int):
        """사용자별 메시지 전송 (모든 연결)"""
        if user_id in self.user_connections:
            for client_id in self.user_connections[user_id].copy():
                await self.send_personal_message(message, client_id)
                
    async def broadcast(self, message: dict):
        """전체 브로드캐스트"""
        for client_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, client_id)

# 연결 매니저 인스턴스
manager = ConnectionManager()

# 신호 데이터 저장소 (실제로는 Redis나 DB 사용 권장)
signal_storage = {
    'latest_signals': [],
    'trading_status': {},
    'market_data': {}
}

@app.get("/")
async def get():
    """WebSocket 테스트 페이지"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
        <head>
            <title>WebSocket Test</title>
        </head>
        <body>
            <h1>OPENSYSTEMS BOT WebSocket Test</h1>
            <div id="messages"></div>
            <script>
                var ws = new WebSocket("ws://localhost:8000/ws/test-client");
                ws.onmessage = function(event) {
                    var messages = document.getElementById('messages');
                    var message = document.createElement('div');
                    message.textContent = event.data;
                    messages.appendChild(message);
                };
            </script>
        </body>
    </html>
    """)

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """기본 WebSocket 엔드포인트"""
    await manager.connect(websocket, client_id)
    
    try:
        # 연결 확인 메시지
        await manager.send_personal_message({
            "type": "connection",
            "message": "WebSocket 연결 성공",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
        while True:
            # 클라이언트로부터 메시지 수신
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 메시지 타입별 처리
            await handle_message(message, client_id)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket 오류 ({client_id}): {e}")
        manager.disconnect(client_id)

@app.websocket("/ws/trading/{user_id}")
async def trading_websocket(websocket: WebSocket, user_id: int):
    """트레이딩 전용 WebSocket 엔드포인트"""
    client_id = f"trading_{user_id}_{datetime.now().timestamp()}"
    await manager.connect(websocket, client_id, user_id)
    
    try:
        # 초기 데이터 전송
        await send_initial_data(client_id, user_id)
        
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            message['user_id'] = user_id
            
            await handle_trading_message(message, client_id, user_id)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id, user_id)
    except Exception as e:
        logger.error(f"트레이딩 WebSocket 오류 (사용자 {user_id}): {e}")
        manager.disconnect(client_id, user_id)

async def handle_message(message: dict, client_id: str):
    """일반 메시지 처리"""
    msg_type = message.get('type', 'unknown')
    
    if msg_type == 'ping':
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
    elif msg_type == 'echo':
        await manager.send_personal_message({
            "type": "echo_response",
            "original_message": message,
            "timestamp": datetime.now().isoformat()
        }, client_id)

async def handle_trading_message(message: dict, client_id: str, user_id: int):
    """트레이딩 메시지 처리"""
    msg_type = message.get('type', 'unknown')
    
    if msg_type == 'subscribe_signals':
        # 신호 구독
        await manager.send_personal_message({
            "type": "subscription_confirmed",
            "subscription": "trading_signals",
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        }, client_id)
        
    elif msg_type == 'bot_command':
        # 봇 명령 처리
        command = message.get('command')
        await process_bot_command(command, user_id, client_id)
        
    elif msg_type == 'get_status':
        # 상태 조회
        await send_trading_status(client_id, user_id)

async def send_initial_data(client_id: str, user_id: int):
    """초기 데이터 전송"""
    await manager.send_personal_message({
        "type": "initial_data",
        "user_id": user_id,
        "latest_signals": signal_storage['latest_signals'][-10:],
        "trading_status": signal_storage['trading_status'].get(user_id, {}),
        "timestamp": datetime.now().isoformat()
    }, client_id)

async def process_bot_command(command: str, user_id: int, client_id: str):
    """봇 명령 처리"""
    if command == 'start':
        # 봇 시작 로직
        signal_storage['trading_status'][user_id] = {
            'status': 'running',
            'start_time': datetime.now().isoformat()
        }
        
        await manager.send_user_message({
            "type": "bot_status_update",
            "status": "running",
            "message": "봇이 시작되었습니다",
            "timestamp": datetime.now().isoformat()
        }, user_id)
        
    elif command == 'stop':
        # 봇 중지 로직
        signal_storage['trading_status'][user_id] = {
            'status': 'stopped',
            'stop_time': datetime.now().isoformat()
        }
        
        await manager.send_user_message({
            "type": "bot_status_update",
            "status": "stopped",
            "message": "봇이 중지되었습니다",
            "timestamp": datetime.now().isoformat()
        }, user_id)

async def send_trading_status(client_id: str, user_id: int):
    """트레이딩 상태 전송"""
    status = signal_storage['trading_status'].get(user_id, {})
    
    await manager.send_personal_message({
        "type": "trading_status",
        "status": status,
        "timestamp": datetime.now().isoformat()
    }, client_id)

# 백그라운드 태스크: 신호 브로드캐스트
async def signal_broadcaster():
    """신호 브로드캐스트 백그라운드 태스크"""
    while True:
        try:
            # 실제로는 SignalR이나 다른 소스에서 신호를 받아옴
            # 여기서는 시뮬레이션
            await asyncio.sleep(5)  # 5초마다
            
            # 새 신호 생성 (시뮬레이션)
            new_signal = {
                "type": "trading_signal",
                "signal_type": "long" if datetime.now().second % 2 == 0 else "short",
                "symbol": "BTCUSDT",
                "price": 45000 + (datetime.now().second * 10),
                "timestamp": datetime.now().isoformat()
            }
            
            # 신호 저장
            signal_storage['latest_signals'].append(new_signal)
            if len(signal_storage['latest_signals']) > 100:
                signal_storage['latest_signals'] = signal_storage['latest_signals'][-100:]
            
            # 모든 연결된 클라이언트에 브로드캐스트
            await manager.broadcast(new_signal)
            
        except Exception as e:
            logger.error(f"신호 브로드캐스트 오류: {e}")
            await asyncio.sleep(1)

# 앱 시작 시 백그라운드 태스크 실행
@app.on_event("startup")
async def startup_event():
    """앱 시작 시 실행"""
    init_db()
    logger.info("FastAPI WebSocket 서버 시작")
    
    # 백그라운드 태스크 시작
    asyncio.create_task(signal_broadcaster())

if __name__ == "__main__":
    uvicorn.run(
        "fastapi_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
