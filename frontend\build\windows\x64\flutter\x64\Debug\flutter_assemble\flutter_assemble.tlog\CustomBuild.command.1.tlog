^D:\AUGMENT-PROJECTS\OPENSYSTEMS_BOT_V3\BUILD\WINDOWS\X64\CMAKEFILES\CDD45167F6CD4892751AB76A15347421\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\src\flutter PROJECT_DIR=D:\augment-projects\OPENSYSTEMS_BOT_V3 FLUTTER_ROOT=C:\src\flutter FLUTTER_EPHEMERAL_DIR=D:\augment-projects\OPENSYSTEMS_BOT_V3\windows\flutter\ephemeral PROJECT_DIR=D:\augment-projects\OPENSYSTEMS_BOT_V3 FLUTTER_TARGET=D:\augment-projects\OPENSYSTEMS_BOT_V3\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\augment-projects\OPENSYSTEMS_BOT_V3\.dart_tool\package_config.json C:/src/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AUGMENT-PROJECTS\OPENSYSTEMS_BOT_V3\BUILD\WINDOWS\X64\CMAKEFILES\2E3035D469EA0C4D7A64AD4C1B2916D4\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\AUGMENT-PROJECTS\OPENSYSTEMS_BOT_V3\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/augment-projects/OPENSYSTEMS_BOT_V3/windows -BD:/augment-projects/OPENSYSTEMS_BOT_V3/build/windows/x64 --check-stamp-file D:/augment-projects/OPENSYSTEMS_BOT_V3/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
