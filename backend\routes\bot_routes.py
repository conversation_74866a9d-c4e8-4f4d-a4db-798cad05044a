# 봇 제어 API 라우트
# routes/bot_routes.py
import logging
from datetime import datetime, timezone
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from database import db_session
from models.user import User
from models.position import Position
from models.trade import Trade
from api_client.bybit_v5_client import BybitV5Client
from bot_core.trading_logic import TradingLogic
from bot_core import SignalProcessor
from bot_core.mode_manager import ModeManager

logger = logging.getLogger("opensystems_bot")

# admin.json 로더 함수 제거 - 이제 SQLite 데이터베이스 사용

bot_bp = Blueprint('bot', __name__)

# 전역 변수 - 활성 봇 인스턴스들
active_bots = {}  # {user_id: {'trading_logic': TradingLogic, 'signal_processor': SignalProcessor, 'mode_manager': ModeManager}}

@bot_bp.route('/start', methods=['POST'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def start_bot():
    """봇 시작"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 이미 실행 중인지 확인
        if user_id in active_bots:
            return jsonify({
                'success': False,
                'message': '봇이 이미 실행 중입니다'
            }), 400

        # 모드 관리자 초기화
        mode_manager = ModeManager(user_id, BybitV5Client)

        # API 클라이언트 초기화
        api_client = mode_manager.initialize_client()
        if not api_client:
            return jsonify({
                'success': False,
                'error': 'API 연결에 실패했습니다. API 키를 확인해주세요.'
            }), 400

        # 매매 로직 초기화
        # 레버리지 값 추출 (예: "10X" -> 10)
        leverage_str = mode_manager.setting.leverage
        leverage = int(''.join(filter(str.isdigit, leverage_str))) if leverage_str else 10

        # Investment Ratio 가져오기 (예: "100%" -> 사용자 설정값)
        investment_ratio = mode_manager.setting.investment_ratio

        # 🟢 헷징 임계값 가져오기 (예: "-1.0%" -> 사용자 설정값)
        hedging_threshold = mode_manager.setting.hedging_threshold

        trading_logic = TradingLogic(
            user_id=user_id,
            api_client=api_client,
            symbol=mode_manager.setting.symbol,
            leverage=leverage,
            investment_ratio=investment_ratio,
            hedging_threshold=hedging_threshold
        )

        # 신호 처리기 초기화
        signal_processor = SignalProcessor(
            user_id=user_id,
            trading_logic=trading_logic
        )

        # TradingLogic에 SignalProcessor 참조 설정 (거래 로그 전송용)
        trading_logic.signal_processor = signal_processor

        # 신호 처리 시작
        if signal_processor.start():
            # 활성 봇 목록에 추가
            active_bots[user_id] = {
                'trading_logic': trading_logic,
                'signal_processor': signal_processor,
                'mode_manager': mode_manager,
                'start_time': datetime.now(timezone.utc)
            }

            logger.info(f"봇 시작됨 - 사용자: {user_id}")

            # 시스템 로그 전송
            trading_logic.send_system_log("🚀 봇이 성공적으로 시작되었습니다", "SYSTEM")

            return jsonify({
                'success': True,
                'message': '봇이 시작되었습니다',
                'bot_status': trading_logic.get_status(),
                'mode_info': mode_manager.get_mode_info()
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': '신호 처리기 시작에 실패했습니다'
            }), 500

    except Exception as e:
        logger.error(f"봇 시작 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bot_bp.route('/stop', methods=['POST'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def stop_bot():
    """봇 중지"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 실행 중인 봇 확인
        if user_id not in active_bots:
            return jsonify({
                'success': False,
                'message': '실행 중인 봇이 없습니다'
            }), 400

        # 봇 중지 (모든 포지션 청산 포함)
        bot_instance = active_bots[user_id]
        signal_processor = bot_instance['signal_processor']
        trading_logic = bot_instance.get('trading_logic')

        # 시스템 로그 전송 (봇 중지 전에 전송)
        if trading_logic:
            trading_logic.send_system_log("🛑 모든 포지션 청산 후 봇 중지 시작", "SYSTEM")

        # 포지션 청산 후 봇 중지
        signal_processor.stop()

        # 활성 봇 목록에서 제거
        del active_bots[user_id]

        logger.info(f"모든 포지션 청산 후 봇 중지 완료 - 사용자: {user_id}")

        return jsonify({
            'success': True,
            'message': '모든 포지션이 청산되고 봇이 중지되었습니다'
        }), 200

    except Exception as e:
        logger.error(f"봇 중지 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bot_bp.route('/status', methods=['GET'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def get_bot_status():
    """봇 상태 조회"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 봇 실행 여부 확인
        if user_id not in active_bots:
            return jsonify({
                'is_running': False,
                'status': 'IDLE',
                'message': '봇이 실행되지 않았습니다'
            }), 200

        # 봇 상태 조회
        bot_instance = active_bots[user_id]
        trading_logic = bot_instance['trading_logic']
        signal_processor = bot_instance['signal_processor']
        mode_manager = bot_instance['mode_manager']
        start_time = bot_instance['start_time']

        # 업타임 계산
        uptime_seconds = int((datetime.now(timezone.utc) - start_time).total_seconds())
        uptime_formatted = f"{uptime_seconds // 3600:02d}:{(uptime_seconds % 3600) // 60:02d}:{uptime_seconds % 60:02d}"

        return jsonify({
            'is_running': True,
            'uptime': uptime_formatted,
            'uptime_seconds': uptime_seconds,
            'bot_status': trading_logic.get_status(),
            'signal_status': signal_processor.get_connection_status(),
            'mode_info': mode_manager.get_mode_info(),
            'stats': signal_processor.get_stats()
        }), 200

    except Exception as e:
        logger.error(f"봇 상태 조회 오류: {e}")
        return jsonify({
            'is_running': False,
            'error': str(e)
        }), 500

@bot_bp.route('/positions/sync', methods=['POST'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def sync_positions():
    """바이비트 실제 포지션과 시스템 동기화"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 봇이 실행 중인지 확인
        if user_id not in active_bots:
            return jsonify({'error': '봇이 실행되지 않았습니다'}), 400

        # API 클라이언트 가져오기
        api_client = active_bots[user_id]['api_client']

        # 바이비트에서 실제 포지션 조회
        actual_positions = api_client.get_positions('ETHUSDT')

        # 시스템 포지션 상태 업데이트
        trading_logic = active_bots[user_id]['trading_logic']

        if actual_positions and actual_positions.get('success'):
            positions_data = actual_positions.get('positions', [])

            # 포지션 상태 초기화
            trading_logic.current_positions = {'long': None, 'short': None}

            # 실제 포지션으로 업데이트
            for pos in positions_data:
                if float(pos.get('size', 0)) > 0:  # 포지션이 존재하는 경우
                    side = 'long' if pos.get('side') == 'Buy' else 'short'
                    trading_logic.current_positions[side] = {
                        'side': side.upper(),
                        'entry_price': float(pos.get('avgPrice', 0)),
                        'size': float(pos.get('size', 0)),
                        'unrealised_pnl': float(pos.get('unrealisedPnl', 0))
                    }

            return jsonify({
                'success': True,
                'message': '포지션 동기화 완료',
                'positions': trading_logic.current_positions
            })
        else:
            return jsonify({'error': '포지션 조회 실패'}), 500

    except Exception as e:
        logger.error(f"포지션 동기화 오류: {e}")
        return jsonify({'error': str(e)}), 500

@bot_bp.route('/account/stats', methods=['GET'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def get_account_stats():
    """실시간 계좌 자산 정보 조회"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 봇 실행 여부와 관계없이 자산 정보 제공
        # 사용자가 로그인하고 API 키가 설정되어 있으면 자산 정보 표시

        # ModeManager 초기화 (봇이 중지되어도 API 클라이언트 사용 가능)
        from bot_core.mode_manager import ModeManager
        from api_client.bybit_v5_client import BybitV5Client
        mode_manager = ModeManager(user_id, BybitV5Client)
        api_client = mode_manager.get_client()

        if not api_client:
            return jsonify({
                'total_equity': 0.0,
                'total_profit': 0.0,
                'last_update': datetime.now().strftime('%H:%M:%S'),
                'is_connected': False,
                'error': 'API 클라이언트를 초기화할 수 없습니다'
            })

        # 계좌 잔고 조회

        # 계좌 잔고 조회
        balance_result = api_client.get_balance()
        if not balance_result or not isinstance(balance_result, dict):
            total_equity = 0.0
        else:
            total_equity = balance_result.get('available_balance', 0.0)

        # 포지션 평가손익 및 Total Profit 계산
        position_pnl = 0.0
        total_profit = 0.0

        # 봇이 실행 중인 경우에만 포지션 PnL과 Total Profit 계산
        if user_id in active_bots:
            trading_logic = active_bots[user_id]['trading_logic']
            position_pnl = trading_logic._calculate_current_position_pnl()
            total_profit = trading_logic.calculate_total_profit()
        else:
            # 봇이 중지된 상태에서는 포지션 PnL만 API에서 직접 조회
            try:
                positions = api_client.get_positions("ETHUSDT")
                for pos in positions:
                    if float(pos.get('size', 0)) != 0:
                        position_pnl += float(pos.get('unrealisedPnl', 0))
            except Exception as e:
                logger.warning(f"포지션 PnL 조회 오류 (봇 중지 상태): {e}")
                position_pnl = 0.0

        # 총 자산 = 잔고 + 포지션 평가손익
        total_equity += position_pnl

        return jsonify({
            'total_equity': round(total_equity, 2),
            'total_profit': round(total_profit, 2),
            'last_update': datetime.now().strftime('%H:%M:%S'),
            'is_connected': True,
            'position_pnl': round(position_pnl, 2),
            'balance': round(total_equity - position_pnl, 2) if total_equity > position_pnl else 0.0
        })

    except Exception as e:
        logger.error(f"계좌 통계 조회 오류: {e}")
        return jsonify({
            'total_equity': 0.0,
            'total_profit': 0.0,
            'last_update': datetime.now().strftime('%H:%M:%S'),
            'is_connected': False,
            'error': str(e)
        }), 500

@bot_bp.route('/positions', methods=['GET'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def get_positions():
    """현재 포지션 조회"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 실행 중인 봇 확인
        if user_id not in active_bots:
            return jsonify({
                'success': False,
                'message': '실행 중인 봇이 없습니다'
            }), 400

        # 봇에서 API 클라이언트 가져오기
        bot_instance = active_bots[user_id]
        mode_manager = bot_instance['mode_manager']
        api_client = mode_manager.get_client()

        if not api_client:
            return jsonify({
                'success': False,
                'error': 'API 클라이언트를 찾을 수 없습니다'
            }), 400

        # Bybit에서 실제 포지션 조회
        positions = api_client.get_positions("ETHUSDT")

        # 활성 포지션만 필터링
        active_positions = []
        for pos in positions:
            size = float(pos.get('size', 0))
            if size != 0:
                active_positions.append({
                    'symbol': pos.get('symbol'),
                    'side': pos.get('side'),
                    'size': size,
                    'entry_price': float(pos.get('avgPrice', 0)),
                    'mark_price': float(pos.get('markPrice', 0)),
                    'unrealized_pnl': float(pos.get('unrealisedPnl', 0)),
                    'pnl_percentage': float(pos.get('unrealisedPnl', 0)) / float(pos.get('positionValue', 1)) * 100 if pos.get('positionValue') else 0
                })

        return jsonify({
            'success': True,
            'positions': active_positions,
            'total_positions': len(active_positions),
            'symbol': 'ETHUSDT'
        }), 200

    except Exception as e:
        logger.error(f"포지션 조회 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bot_bp.route('/orders', methods=['GET'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def get_order_history():
    """주문 내역 조회"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 실행 중인 봇 확인
        if user_id not in active_bots:
            return jsonify({
                'success': False,
                'message': '실행 중인 봇이 없습니다'
            }), 400

        # 봇에서 API 클라이언트 가져오기
        bot_instance = active_bots[user_id]
        mode_manager = bot_instance['mode_manager']
        api_client = mode_manager.get_client()

        if not api_client:
            return jsonify({
                'success': False,
                'error': 'API 클라이언트를 찾을 수 없습니다'
            }), 400

        # Bybit에서 최근 주문 내역 조회
        orders = api_client.get_order_history("ETHUSDT", limit=10)

        # 주문 정보 정리
        order_list = []
        for order in orders:
            order_list.append({
                'order_id': order.get('orderId'),
                'symbol': order.get('symbol'),
                'side': order.get('side'),
                'qty': float(order.get('qty', 0)),
                'price': float(order.get('price', 0)) if order.get('price') else 0,
                'order_status': order.get('orderStatus'),
                'created_time': order.get('createdTime'),
                'updated_time': order.get('updatedTime'),
                'cum_exec_qty': float(order.get('cumExecQty', 0)),
                'cum_exec_value': float(order.get('cumExecValue', 0))
            })

        return jsonify({
            'success': True,
            'orders': order_list,
            'total_orders': len(order_list),
            'symbol': 'ETHUSDT'
        }), 200

    except Exception as e:
        logger.error(f"주문 내역 조회 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@bot_bp.route('/account/stats-with-user', methods=['POST'])
def get_account_stats_with_user():
    """계좌 정보 조회 (사용자명 파라미터 방식)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        username = data.get('username')
        if not username:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        # 사용자명으로 사용자 조회
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 봇 실행 여부와 관계없이 자산 정보 제공
        # 사용자가 로그인하고 API 키가 설정되어 있으면 자산 정보 표시

        # ModeManager 초기화 (봇이 중지되어도 API 클라이언트 사용 가능)
        from bot_core.mode_manager import ModeManager
        from api_client.bybit_v5_client import BybitV5Client
        mode_manager = ModeManager(user_id, BybitV5Client)
        api_client = mode_manager.get_client()

        if not api_client:
            return jsonify({
                'total_equity': 0.0,
                'total_profit': 0.0,
                'last_update': datetime.now().strftime('%H:%M:%S'),
                'is_connected': False,
                'error': 'API 클라이언트를 초기화할 수 없습니다'
            })

        # 계좌 잔고 조회
        balance_result = api_client.get_balance()
        if not balance_result or not isinstance(balance_result, dict):
            total_equity = 0.0
        else:
            total_equity = balance_result.get('available_balance', 0.0)

        # 포지션 평가손익 및 Total Profit 계산
        position_pnl = 0.0
        total_profit = 0.0

        # 봇이 실행 중인 경우에만 포지션 PnL과 Total Profit 계산
        if user_id in active_bots:
            trading_logic = active_bots[user_id]['trading_logic']
            position_pnl = trading_logic._calculate_current_position_pnl()
            total_profit = trading_logic.calculate_total_profit()
        else:
            # 봇이 중지된 상태에서는 포지션 PnL만 API에서 직접 조회
            try:
                positions = api_client.get_positions("ETHUSDT")
                for pos in positions:
                    if float(pos.get('size', 0)) != 0:
                        position_pnl += float(pos.get('unrealisedPnl', 0))
            except Exception as e:
                logger.warning(f"포지션 PnL 조회 오류 (봇 중지 상태): {e}")
                position_pnl = 0.0

        # 총 자산 = 잔고 + 포지션 평가손익
        total_equity += position_pnl

        return jsonify({
            'total_equity': round(total_equity, 2),
            'total_profit': round(total_profit, 2),
            'last_update': datetime.now().strftime('%H:%M:%S'),
            'is_connected': True,
            'position_pnl': round(position_pnl, 2),
            'balance': round(total_equity - position_pnl, 2) if total_equity > position_pnl else 0.0
        })

    except Exception as e:
        logger.error(f"계좌 통계 조회 오류 (사용자명 방식): {e}")
        return jsonify({
            'total_equity': 0.0,
            'total_profit': 0.0,
            'last_update': datetime.now().strftime('%H:%M:%S'),
            'is_connected': False,
            'error': str(e)
        }), 500

@bot_bp.route('/update-settings', methods=['PUT'])
def update_bot_settings():
    """🟢 실행 중인 봇의 설정값 동적 업데이트"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '업데이트할 설정이 없습니다'}), 400

        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        user_id = user.id

        # 활성 봇 확인
        if user_id not in active_bots:
            return jsonify({'success': False, 'error': '실행 중인 봇이 없습니다'}), 404

        bot_info = active_bots[user_id]
        trading_logic = bot_info['trading_logic']
        mode_manager = bot_info['mode_manager']

        # 🟢 1단계: DB에 설정 저장
        db_update_data = {}
        if 'symbol' in data:
            db_update_data['symbol'] = data['symbol']
        if 'leverage' in data:
            db_update_data['leverage'] = data['leverage']
        if 'investment_ratio' in data:
            db_update_data['investment_ratio'] = data['investment_ratio']
        if 'hedging_threshold' in data:
            db_update_data['hedging_threshold'] = data['hedging_threshold']

        db_result = {'success': True}
        if db_update_data:
            db_result = mode_manager.update_settings(**db_update_data)
            if not db_result['success']:
                return jsonify({'success': False, 'error': f'DB 설정 저장 실패: {db_result.get("error")}'}), 500

        # 🟢 2단계: 실행 중인 봇에 설정 적용
        bot_update_data = {}
        if 'symbol' in data:
            bot_update_data['symbol'] = data['symbol']
        if 'leverage' in data:
            # 레버리지 문자열에서 숫자 추출 (예: "10X" -> 10)
            leverage_str = data['leverage']
            leverage = int(''.join(filter(str.isdigit, leverage_str))) if leverage_str else 10
            bot_update_data['leverage'] = leverage
        if 'investment_ratio' in data:
            bot_update_data['investment_ratio'] = data['investment_ratio']
        if 'hedging_threshold' in data:
            bot_update_data['hedging_threshold'] = data['hedging_threshold']

        if bot_update_data:
            bot_result = trading_logic.update_settings(**bot_update_data)
            if bot_result['success']:
                logger.info(f"봇 설정 동적 업데이트 성공 - 사용자: {user_id}, 필드: {bot_result['updated_fields']}")
                return jsonify({
                    'success': True,
                    'message': '설정이 실시간으로 업데이트되었습니다',
                    'db_result': db_result,
                    'bot_result': bot_result
                }), 200
            else:
                return jsonify({'success': False, 'error': f'봇 설정 업데이트 실패: {bot_result.get("error")}'}), 500
        else:
            return jsonify({'success': False, 'error': '업데이트할 설정이 없습니다'}), 400

    except Exception as e:
        logger.error(f"봇 설정 업데이트 오류: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
