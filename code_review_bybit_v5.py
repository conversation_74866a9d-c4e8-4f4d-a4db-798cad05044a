#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bybit V5 API 코드 검토 및 문제점 분석 보고서
"""

import sys
import os
import json
from datetime import datetime

def analyze_bybit_v5_code():
    """
    Bybit V5 클라이언트 코드 분석
    """
    
    print("🔍 Bybit V5 API 코드 검토 보고서")
    print("=" * 80)
    print(f"📅 검토 일시: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    issues = []
    
    # 1. API 키 문제 분석
    print("\n1️⃣ API 키 문제 분석")
    print("-" * 40)
    api_key = "u3ZXadFktdLqQ95DZx"
    api_secret = "1vrTxh3PaEJiVhZp8RTPz2CUp7bZ0fwnRXfi"
    
    print(f"API 키 길이: {len(api_key)} (표준: 20자 이상)")
    print(f"API 시크릿 길이: {len(api_secret)} (표준: 32자 이상)")
    
    if len(api_key) < 20:
        issues.append({
            "category": "API 키",
            "severity": "High",
            "issue": "API 키 길이가 표준보다 짧음",
            "detail": f"현재 {len(api_key)}자, 권장 20자 이상",
            "impact": "인증 실패 가능성",
            "solution": "올바른 API 키로 교체 필요"
        })
    
    # 2. 코드 중복 문제
    print("\n2️⃣ 코드 중복 문제")
    print("-" * 40)
    duplicate_methods = [
        "get_balance", "get_ticker", "place_order", "close_position", 
        "get_account_info", "get_trading_symbols", "get_server_time"
    ]
    
    for method in duplicate_methods:
        print(f"⚠️ 중복 메서드 발견: {method}")
        issues.append({
            "category": "코드 품질",
            "severity": "Medium",
            "issue": f"메서드 중복 정의: {method}",
            "detail": "동일한 메서드가 여러 번 정의됨",
            "impact": "코드 유지보수성 저하, 예상치 못한 동작",
            "solution": "중복 메서드 제거 및 통합"
        })
    
    # 3. 무한 재귀 문제
    print("\n3️⃣ 무한 재귀 문제")
    print("-" * 40)
    print("❌ get_ticker() 메서드에서 무한 재귀 발생")
    print("   - get_current_price() → get_ticker() → get_current_price() 순환 호출")
    
    issues.append({
        "category": "로직 오류",
        "severity": "Critical",
        "issue": "무한 재귀 호출",
        "detail": "get_ticker와 get_current_price 메서드 간 순환 참조",
        "impact": "스택 오버플로우, 애플리케이션 크래시",
        "solution": "메서드 로직 수정 필요"
    })
    
    # 4. API 엔드포인트 문제
    print("\n4️⃣ API 엔드포인트 설정")
    print("-" * 40)
    print("⚠️ 데모 모드 설정이 실제 서버로 연결됨")
    print("   - 데모 키로 실제 서버(api.bybit.com) 접근 시도")
    print("   - 인증 실패 원인 중 하나")
    
    issues.append({
        "category": "설정 오류",
        "severity": "High", 
        "issue": "잘못된 API 엔드포인트",
        "detail": "데모 키로 실제 서버 접근",
        "impact": "인증 실패, 기능 제한",
        "solution": "데모 환경에 맞는 API 키 사용 또는 엔드포인트 수정"
    })
    
    # 5. 오류 처리 분석
    print("\n5️⃣ 오류 처리 분석")
    print("-" * 40)
    print("✅ 기본적인 try-catch 구조는 적절히 구현됨")
    print("⚠️ 일부 메서드에서 오류 시 빈 값 반환으로 문제 은폐")
    
    issues.append({
        "category": "오류 처리",
        "severity": "Medium",
        "issue": "불충분한 오류 정보",
        "detail": "오류 발생 시 빈 값 반환으로 문제 파악 어려움",
        "impact": "디버깅 어려움, 사용자 경험 저하",
        "solution": "상세한 오류 정보 반환 및 로깅 강화"
    })
    
    # 6. 테스트 결과 분석
    print("\n6️⃣ 테스트 결과 분석")
    print("-" * 40)
    print("✅ 성공한 기능:")
    print("   - 서버 시간 조회 (공개 API)")
    print("   - 거래 심볼 조회 (공개 API)")
    print("   - 포지션 조회 (빈 결과지만 API 호출 성공)")
    print("\n❌ 실패한 기능:")
    print("   - API 연결 테스트 (인증 실패)")
    print("   - 지갑 잔고 조회 (인증 실패)")
    print("   - 시세 정보 조회 (무한 재귀)")
    
    # 7. 권장 사항
    print("\n7️⃣ 권장 개선 사항")
    print("-" * 40)
    recommendations = [
        "올바른 Demo Trading API 키 발급 및 적용",
        "중복 메서드 제거 및 코드 구조 정리",
        "무한 재귀 문제 해결 (get_ticker 로직 수정)",
        "데모/리얼 환경 분리 로직 개선",
        "상세한 오류 로깅 및 예외 처리 강화",
        "단위 테스트 작성으로 품질 보장",
        "API 호출 제한(Rate Limiting) 구현",
        "설정 파일 분리로 민감 정보 보호"
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"   {i}. {rec}")
    
    # 8. 심각도별 이슈 요약
    print("\n8️⃣ 심각도별 이슈 요약")
    print("-" * 40)
    
    critical = [i for i in issues if i['severity'] == 'Critical']
    high = [i for i in issues if i['severity'] == 'High']
    medium = [i for i in issues if i['severity'] == 'Medium']
    
    print(f"🚨 Critical: {len(critical)}개")
    print(f"⚠️ High: {len(high)}개")
    print(f"💛 Medium: {len(medium)}개")
    print(f"📊 총 이슈: {len(issues)}개")
    
    # 9. 결과 저장
    report_data = {
        "timestamp": datetime.now().isoformat(), 
        "api_key_info": {
            "key_length": len(api_key),
            "secret_length": len(api_secret),
            "key_valid": len(api_key) >= 20
        },
        "issues": issues,
        "recommendations": recommendations,
        "summary": {
            "total_issues": len(issues),
            "critical": len(critical),
            "high": len(high), 
            "medium": len(medium)
        }
    }
    
    with open('bybit_v5_code_review.json', 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 상세 검토 결과가 'bybit_v5_code_review.json'에 저장되었습니다.")
    
    # 10. 최종 결론
    print("\n" + "=" * 80)
    print("📋 최종 결론")
    print("=" * 80)
    
    if len(critical) > 0:
        print("🚨 CRITICAL 이슈 발견: 즉시 수정이 필요합니다.")
        print("   - 무한 재귀 문제로 인한 시스템 크래시 가능성")
    
    if len(high) > 0:
        print("⚠️ HIGH 이슈 발견: 조속한 수정이 권장됩니다.")
        print("   - API 키 및 엔드포인트 설정 문제")
    
    print(f"\n📊 코드 품질 점수: {max(0, 100 - len(issues) * 10)}/100")
    print("💡 권장 조치: 코드 리팩토링 및 테스트 강화")
    
    return report_data

def create_fixed_client():
    """수정된 클라이언트 예제 코드 생성"""
    
    fixed_code = '''# 수정된 Bybit V5 클라이언트 (주요 문제점 해결)
# api_client/bybit_v5_client_fixed.py

import logging
from pybit.unified_trading import HTTP
import time
from datetime import datetime

logger = logging.getLogger("opensystems_bot")

class BybitV5ClientFixed:
    """
    수정된 Bybit API v5 클라이언트
    - 무한 재귀 문제 해결
    - 중복 메서드 제거  
    - 오류 처리 개선
    """
    
    def __init__(self, api_key, api_secret, is_demo=True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.is_demo = is_demo
        
        # 데모 모드에 따른 URL 설정
        if is_demo:
            # 실제로는 Demo Trading 키가 실제 서버를 사용하므로
            # 테스트넷이 아닌 실제 서버 사용
            self.base_url = "https://api.bybit.com"
            testnet = False
        else:
            self.base_url = "https://api.bybit.com"
            testnet = False
        
        self.client = HTTP(
            testnet=testnet,
            api_key=api_key,
            api_secret=api_secret
        )
        
        logger.info(f"BybitV5Client initialized: mode={'Demo' if is_demo else 'Real'}")
    
    def test_connection(self):
        """API 연결 테스트"""
        try:
            # 공개 API로 연결 테스트 (인증 불필요)
            result = self.client.get_server_time()
            if result.get('retCode') == 0:
                logger.info("API connection test successful")
                return True
            else:
                logger.error(f"API connection test failed: {result.get('retMsg')}")
                return False
        except Exception as e:
            logger.error(f"API connection test error: {e}")
            return False
    
    def get_server_time(self):
        """서버 시간 조회 - 중복 제거"""
        try:
            result = self.client.get_server_time()
            if result.get('retCode') == 0:
                server_time = result.get('result', {}).get('timeSecond')
                return {
                    'server_time': server_time,
                    'server_time_ms': result.get('result', {}).get('timeNano'),
                    'local_time': int(time.time())
                }
            else:
                logger.error(f"Failed to get server time: {result.get('retMsg')}")
                return None
        except Exception as e:
            logger.error(f"Error getting server time: {e}")
            return None
    
    def get_ticker_data(self, symbol):
        """티커 데이터 조회 - 무한 재귀 방지"""
        try:
            result = self.client.get_tickers(category="linear", symbol=symbol)
            if result.get('retCode') == 0:
                tickers = result.get('result', {}).get('list', [])
                if tickers:
                    return tickers[0]
                return None
            else:
                logger.error(f"Failed to get ticker: {result.get('retMsg')}")
                return None
        except Exception as e:
            logger.error(f"Error getting ticker: {e}")
            return None
    
    def get_current_price(self, symbol):
        """현재가 조회 - 직접 구현으로 재귀 방지"""
        ticker = self.get_ticker_data(symbol)  # get_ticker 대신 get_ticker_data 사용
        if ticker and 'lastPrice' in ticker:
            return float(ticker['lastPrice'])
        return None
    
    def get_wallet_balance(self):
        """지갑 잔고 조회 - 오류 정보 개선"""
        try:
            result = self.client.get_wallet_balance(accountType="UNIFIED")
            if result.get('retCode') == 0:
                balances = result.get('result', {}).get('list', [])
                if balances:
                    return {
                        'success': True,
                        'data': balances[0]
                    }
                return {
                    'success': True,
                    'data': {},
                    'message': 'No balance data found'
                }
            else:
                return {
                    'success': False,
                    'error_code': result.get('retCode'),
                    'error_msg': result.get('retMsg'),
                    'message': 'Failed to get wallet balance'
                }
        except Exception as e:
            logger.error(f"Error getting wallet balance: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Exception occurred while getting wallet balance'
            }
    
    def get_positions(self, symbol=None):
        """포지션 조회 - 오류 정보 개선"""
        try:
            params = {"category": "linear"}
            if symbol:
                params["symbol"] = symbol
            
            result = self.client.get_positions(**params)
            
            if result.get('retCode') == 0:
                positions = result.get('result', {}).get('list', [])
                return {
                    'success': True,
                    'data': positions,
                    'count': len(positions)
                }
            else:
                return {
                    'success': False,
                    'error_code': result.get('retCode'),
                    'error_msg': result.get('retMsg'),
                    'message': 'Failed to get positions'
                }
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Exception occurred while getting positions'
            }

    # 기타 메서드들도 동일한 패턴으로 개선...
'''
    
    with open('bybit_v5_client_fixed_example.py', 'w', encoding='utf-8') as f:
        f.write(fixed_code)
    
    print("💾 수정된 클라이언트 예제가 'bybit_v5_client_fixed_example.py'에 저장되었습니다.")

def main():
    """메인 함수"""
    try:
        # 코드 분석 실행
        report = analyze_bybit_v5_code()
        
        # 수정된 클라이언트 예제 생성
        create_fixed_client()
        
        print("\n🎯 검토 완료!")
        print("📁 생성된 파일:")
        print("   - bybit_v5_code_review.json (상세 검토 결과)")
        print("   - bybit_v5_client_fixed_example.py (수정 예제)")
        
    except Exception as e:
        print(f"❌ 검토 중 오류 발생: {e}")

if __name__ == "__main__":
    main()
