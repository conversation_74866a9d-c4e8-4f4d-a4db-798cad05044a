# models/log.py
import datetime
import json
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from database import Base
from utils.helpers import utc_now

class Log(Base):
    """로그 모델"""
    __tablename__ = 'logs'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    type = Column(String(20))  # system, signal, trade, error
    message = Column(Text)
    level = Column(String(10))  # INFO, WARNING, ERROR, CRITICAL
    meta_data = Column(Text)  # JSON 형식의 추가 데이터
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    # 관계 설정
    user = relationship("User", back_populates="logs")
    
    def __init__(self, user_id, type, message, level="INFO", metadata=None):
        """
        로그 초기화

        Args:
            user_id: 사용자 ID
            type: 로그 타입 (system, signal, trade, error)
            message: 로그 메시지
            level: 로그 레벨 (INFO, WAR<PERSON>NG, ERROR, CRITICAL)
            metadata: 추가 메타데이터
        """
        self.user_id = user_id
        self.type = type
        self.message = message
        self.level = level

        if metadata:
            self.set_metadata(metadata)

        self.created_at = utc_now()

    def set_metadata(self, metadata):
        """메타데이터 설정"""
        if isinstance(metadata, dict):
            self.meta_data = json.dumps(metadata)
        elif isinstance(metadata, str):
            self.meta_data = metadata
        else:
            self.meta_data = json.dumps({})

    def get_metadata(self):
        """메타데이터 조회"""
        if not self.meta_data:
            return {}

        try:
            return json.loads(self.meta_data)
        except json.JSONDecodeError:
            return {}

    def to_dict(self):
        """
        로그 정보를 딕셔너리로 변환

        Returns:
            로그 정보 딕셔너리
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'type': self.type,
            'message': self.message,
            'level': self.level,
            'metadata': self.get_metadata(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f"<Log(id={self.id}, user_id={self.user_id}, type='{self.type}', level='{self.level}')>"