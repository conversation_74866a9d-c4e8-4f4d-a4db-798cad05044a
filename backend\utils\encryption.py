# utils/encryption.py
import base64
import os
import logging
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger("opensystems_bot")

class EncryptionService:
    """데이터 암호화 및 복호화를 위한 서비스 클래스"""
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        """싱글톤 패턴 구현"""
        if cls._instance is None:
            cls._instance = super(EncryptionService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, app_secret_key=None, salt=None):
        """
        암호화 서비스 초기화
        
        Args:
            app_secret_key: 애플리케이션 시크릿 키. 없으면 환경 변수에서 가져옴
            salt: 암호화 솔트. 없으면 환경 변수에서 가져옴
        """
        # 이미 초기화된 경우 중복 초기화 방지
        if self._initialized:
            return
        
        # 시크릿 키 설정
        if app_secret_key is None:
            # 환경 변수에서 시크릿 키 가져오기
            app_secret_key = os.environ.get('ENCRYPTION_KEY', 'opensystems_secret_key')
            if not app_secret_key:
                logger.warning("ENCRYPTION_KEY 환경 변수가 설정되어 있지 않습니다. 기본값을 사용합니다.")
        
        # 솔트 설정
        if salt is None:
            # 환경 변수에서 솔트 가져오기
            salt = os.environ.get('ENCRYPTION_SALT', 'opensystems_salt')
            if not salt:
                logger.warning("ENCRYPTION_SALT 환경 변수가 설정되어 있지 않습니다. 기본값을 사용합니다.")
        
        # 마스터 키 생성 (애플리케이션 시크릿 키로부터)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(app_secret_key.encode()))
        self.cipher_suite = Fernet(key)
        
        self._initialized = True
        logger.info("EncryptionService initialized")
    
    def encrypt(self, data):
        """
        데이터 암호화
        
        Args:
            data: 암호화할 문자열 데이터
            
        Returns:
            암호화된 문자열
        """
        if not data:
            return None
            
        try:
            # 데이터가 문자열인지 확인하고 바이트로 변환
            if isinstance(data, str):
                data = data.encode()
            
            # 암호화 및 base64 인코딩된 문자열로 반환
            encrypted_data = self.cipher_suite.encrypt(data)
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            return None
    
    def decrypt(self, encrypted_data):
        """
        데이터 복호화
        
        Args:
            encrypted_data: 복호화할 암호화된 문자열
            
        Returns:
            복호화된 원본 문자열
        """
        if not encrypted_data:
            return None
            
        try:
            # base64 디코딩 후 복호화
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            return None


# 싱글톤 인스턴스 생성
encryption_service = EncryptionService()

def encrypt_data(data):
    """데이터 암호화 헬퍼 함수"""
    return encryption_service.encrypt(data)

def decrypt_data(encrypted_data):
    """데이터 복호화 헬퍼 함수"""
    return encryption_service.decrypt(encrypted_data)