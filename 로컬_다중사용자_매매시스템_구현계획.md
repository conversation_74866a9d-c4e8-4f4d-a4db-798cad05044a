# 📋 **로컬 다중 사용자 동시 매매 시스템 구현 계획서**

## **방안 1: 공유 SignalR + 사용자별 처리 아키텍처**

---

## 🎯 **1. 시스템 개요**

### **1.1 현재 문제점**
- ❌ **단일 사용자 처리**: admin 계정만 매매 실행
- ❌ **외부 사용자 기만**: 봇 시작해도 실제 매매 안됨
- ❌ **하드코딩된 API**: 모든 봇 기능이 <EMAIL> 고정

### **1.2 목표 아키텍처**
```
SignalR 서버 (포트 5000)
    ↓ (단일 신호 브로드캐스트)
글로벌 SignalR 매니저 (로컬 백엔드)
    ↓ (신호 분배)
    ├── User A SignalProcessor → User A 독립 매매
    ├── User B SignalProcessor → User B 독립 매매
    ├── User C SignalProcessor → User C 독립 매매
    └── Admin SignalProcessor → Admin 독립 매매

외부 클라이언트/모바일
    ↓ (봇 시작/정지 명령)
해당 사용자의 실제 봇 제어
```

### **1.3 핵심 장점**
- ✅ **효율적 리소스 사용**: SignalR 연결 1개만 필요
- ✅ **실제 다중 사용자 매매**: 모든 사용자 동시 매매 실행
- ✅ **독립적 관리**: 각자의 API 키, 설정, 수익 관리
- ✅ **외부 제어 가능**: 모바일/외부에서 실제 봇 제어

---

## 🔧 **2. 구현 단계별 계획**

### **2.1 1단계: 글로벌 SignalR 매니저 구현**

#### **파일**: `backend/bot_core/global_signal_manager.py` (신규 생성)

**주요 기능:**
- 싱글톤 패턴으로 전역 관리
- 단일 SignalR 연결로 모든 사용자 처리
- 신호 분배 및 사용자 등록/해제 관리

**핵심 클래스:**
```python
class GlobalSignalManager:
    """글로벌 SignalR 신호 관리자 - 단일 연결로 모든 사용자 처리"""
    
    def __init__(self):
        self.signalr_client = SignalRClient("http://localhost:5000/signalr")
        self.active_processors: Dict[int, SignalProcessor] = {}
        self.is_running = False
        
    def start(self) -> bool:
        """글로벌 SignalR 연결 및 신호 분배 시작"""
        
    def register_user(self, user_id: int, signal_processor: SignalProcessor):
        """사용자 SignalProcessor 등록"""
        
    def _distribute_signal_to_all_users(self, signal_data: dict):
        """모든 활성 사용자에게 신호 분배"""
```

### **2.2 2단계: SignalProcessor 수정**

#### **파일**: `backend/bot_core/__init__.py` (기존 파일 수정)

**주요 변경사항:**
- 글로벌 매니저 사용 옵션 추가
- 개별 SignalR 연결 vs 글로벌 매니저 선택 가능
- 사용자별 독립적인 신호 처리

**핵심 수정:**
```python
class SignalProcessor:
    def __init__(self, user_id: int, trading_logic: TradingLogic, use_global_manager: bool = True):
        self.use_global_manager = use_global_manager
        
        if use_global_manager:
            from bot_core.global_signal_manager import global_signal_manager
            self.global_manager = global_signal_manager
        else:
            self.signalr_client = SignalRClient("http://localhost:5000/signalr")
```

### **2.3 3단계: 봇 API 사용자별 처리로 변경**

#### **파일**: `backend/routes/bot_routes.py` (기존 파일 수정)

**새로운 API 엔드포인트:**
- `POST /api/bot/start-with-user` - 사용자별 봇 시작
- `POST /api/bot/stop-with-user` - 사용자별 봇 중지
- `POST /api/bot/status-with-user` - 사용자별 봇 상태 조회
- `GET /api/bot/global-status` - 글로벌 봇 상태 조회

**핵심 변경:**
```python
@bot_bp.route('/start-with-user', methods=['POST'])
def start_bot_with_user():
    """사용자별 봇 시작 (새로운 API)"""
    data = request.get_json()
    username = data.get('username')
    
    user = User.query.filter_by(username=username).first()
    user_id = user.id
    
    # 사용자별 독립 봇 시작
    # 글로벌 SignalR 매니저에 등록
```

### **2.4 4단계: 프론트엔드 API 연결 수정**

#### **파일**: `frontend/lib/main.dart` (기존 파일 수정)

**주요 변경사항:**
- 기존 하드코딩된 API를 사용자별 API로 변경
- 현재 로그인한 사용자명을 API 요청에 포함
- 실제 사용자별 봇 제어 구현

**핵심 수정:**
```dart
Future<void> startBot() async {
  final requestBody = {'username': currentUsername};
  
  final response = await http.post(
    Uri.parse('$apiBaseUrl/api/bot/start-with-user'),  // 새로운 API
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode(requestBody),
  );
}
```

---

## 📊 **3. 구현 후 예상 동작**

### **3.1 시스템 시작 시**
1. **백엔드 시작**: Flask 서버 실행 (포트 3000)
2. **SignalR 서버**: 이미 실행 중 (포트 5000)
3. **글로벌 매니저**: 자동으로 SignalR 연결 대기 상태

### **3.2 사용자 봇 시작 시**
1. **외부/모바일**: `POST /api/bot/start-with-user {"username": "user1"}`
2. **백엔드 처리**: 
   - user1의 API 키로 TradingLogic 초기화
   - SignalProcessor 생성 및 글로벌 매니저에 등록
   - 글로벌 매니저가 아직 시작 안됐으면 자동 시작
3. **결과**: user1 봇이 로컬에서 실제 실행됨

### **3.3 SignalR 신호 수신 시**
1. **신호 수신**: SignalR 서버에서 "롱 신호가 감지되었습니다!" 전송
2. **글로벌 매니저**: 신호를 모든 활성 사용자에게 분배
3. **동시 매매**: 
   - user1 → user1의 API 키로 매매 실행
   - user2 → user2의 API 키로 매매 실행
   - admin → admin의 API 키로 매매 실행
4. **독립적 결과**: 각자의 계좌에서 독립적인 매매 수행

---

## 🚀 **4. 구현 우선순위**

### **Phase 1: 핵심 구조 구현**
1. GlobalSignalManager 클래스 구현
2. SignalProcessor 글로벌 매니저 연동
3. 기본 신호 분배 로직 구현

### **Phase 2: API 개선**
1. 사용자별 봇 제어 API 구현
2. 기존 하드코딩 제거
3. 에러 처리 및 로깅 강화

### **Phase 3: 프론트엔드 연동**
1. 새로운 API 연결
2. 사용자별 봇 상태 표시
3. 실시간 상태 업데이트

### **Phase 4: 테스트 및 최적화**
1. 다중 사용자 동시 매매 테스트
2. 성능 최적화
3. 안정성 검증

---

## ⚠️ **5. 주의사항 및 고려사항**

### **5.1 리소스 관리**
- 동시 사용자 수에 따른 메모리 사용량 모니터링
- API 호출 빈도 제한 고려
- 로그 파일 크기 관리

### **5.2 보안**
- 사용자별 API 키 안전한 저장
- 봇 제어 권한 검증
- 외부 접근 제한

### **5.3 안정성**
- SignalR 연결 끊김 시 자동 재연결
- 개별 사용자 오류가 전체 시스템에 영향 없도록 격리
- 백업 및 복구 방안

---

## 📈 **6. 예상 효과**

### **6.1 사용자 경험 개선**
- ✅ **실제 매매**: 외부 사용자도 진짜 매매 실행
- ✅ **독립적 수익**: 각자의 계좌에서 독립 관리
- ✅ **실시간 제어**: 모바일/외부에서 실제 봇 제어

### **6.2 시스템 효율성**
- ✅ **리소스 최적화**: 단일 SignalR 연결로 모든 사용자 처리
- ✅ **확장성**: 사용자 증가에 유연하게 대응
- ✅ **유지보수성**: 중앙 집중식 신호 관리

### **6.3 비즈니스 가치**
- ✅ **진정한 다중 사용자 서비스**: 더 이상 기만 없음
- ✅ **사용자 만족도 향상**: 실제 작동하는 봇 서비스
- ✅ **서비스 신뢰성**: 투명하고 정직한 매매 시스템

---

## 🎯 **7. 결론**

이 구현 계획을 통해 **진정한 다중 사용자 암호화폐 매매 봇 시스템**을 구축할 수 있습니다.

**핵심 성과:**
- 모든 사용자가 실제로 매매를 실행하는 시스템
- 효율적인 리소스 사용으로 확장 가능한 구조
- 외부 클라이언트에서 실제 봇 제어 가능

**예상 작업 시간: 2-3시간**
**구현 복잡도: 중간**
**효과: 완전한 다중 사용자 매매 시스템**
