# 설정 모델
# models/setting.py
import datetime
from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from database import Base
from utils.encryption import EncryptionService
from utils.helpers import utc_now

# 암호화 서비스 인스턴스
encryption_service = EncryptionService()

class Setting(Base):
    """사용자 설정 모델"""
    __tablename__ = 'settings'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), unique=True)
    symbol = Column(String(20), default="BTCUSDT")
    leverage = Column(String(10), default="5X")
    investment_ratio = Column(String(10), default="25%")
    hedging_threshold = Column(String(10), default="-1.0%")  # 🟢 헷징 임계값 설정
    is_demo = Column(Boolean, default=True)
    
    # 암호화된 API 키 (데모 모드)
    _demo_api_key_encrypted = Column('demo_api_key_encrypted', Text)
    _demo_api_secret_encrypted = Column('demo_api_secret_encrypted', Text)
    
    # 암호화된 API 키 (리얼 모드)
    _real_api_key_encrypted = Column('real_api_key_encrypted', Text)
    _real_api_secret_encrypted = Column('real_api_secret_encrypted', Text)
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.datetime.utcnow)
    
    # 관계 설정
    user = relationship("User", back_populates="setting")
    
    def __init__(self, user_id, symbol="BTCUSDT", leverage="5X", investment_ratio="25%", hedging_threshold="-1.0%", is_demo=True):
        """
        설정 초기화

        Args:
            user_id: 사용자 ID
            symbol: 거래 심볼
            leverage: 레버리지
            investment_ratio: 투자 비율
            hedging_threshold: 헷징 임계값
            is_demo: 데모 모드 여부
        """
        self.user_id = user_id
        self.symbol = symbol
        self.leverage = leverage
        self.investment_ratio = investment_ratio
        self.hedging_threshold = hedging_threshold
        self.is_demo = is_demo
        self.created_at = utc_now()
    
    # 데모 모드 API 키 처리 (암호화)
    @hybrid_property
    def demo_api_key(self):
        """데모 API 키 복호화"""
        if not self._demo_api_key_encrypted:
            return None
        return encryption_service.decrypt(self._demo_api_key_encrypted)
    
    @demo_api_key.setter
    def demo_api_key(self, value):
        """데모 API 키 암호화"""
        if value:
            self._demo_api_key_encrypted = encryption_service.encrypt(value)
        else:
            self._demo_api_key_encrypted = None
    
    @hybrid_property
    def demo_api_secret(self):
        """데모 API 시크릿 복호화"""
        if not self._demo_api_secret_encrypted:
            return None
        return encryption_service.decrypt(self._demo_api_secret_encrypted)
    
    @demo_api_secret.setter
    def demo_api_secret(self, value):
        """데모 API 시크릿 암호화"""
        if value:
            self._demo_api_secret_encrypted = encryption_service.encrypt(value)
        else:
            self._demo_api_secret_encrypted = None
    
    # 리얼 모드 API 키 처리 (암호화)
    @hybrid_property
    def real_api_key(self):
        """리얼 API 키 복호화"""
        if not self._real_api_key_encrypted:
            return None
        return encryption_service.decrypt(self._real_api_key_encrypted)
    
    @real_api_key.setter
    def real_api_key(self, value):
        """리얼 API 키 암호화"""
        if value:
            self._real_api_key_encrypted = encryption_service.encrypt(value)
        else:
            self._real_api_key_encrypted = None
    
    @hybrid_property
    def real_api_secret(self):
        """리얼 API 시크릿 복호화"""
        if not self._real_api_secret_encrypted:
            return None
        return encryption_service.decrypt(self._real_api_secret_encrypted)
    
    @real_api_secret.setter
    def real_api_secret(self, value):
        """리얼 API 시크릿 암호화"""
        if value:
            self._real_api_secret_encrypted = encryption_service.encrypt(value)
        else:
            self._real_api_secret_encrypted = None
    
    def has_demo_api(self):
        """데모 API 키 설정 여부"""
        return bool(self.demo_api_key and self.demo_api_secret)
    
    def has_real_api(self):
        """리얼 API 키 설정 여부"""
        return bool(self.real_api_key and self.real_api_secret)
    
    def to_dict(self, include_private=False):
        """
        설정 정보를 딕셔너리로 변환
        
        Args:
            include_private: 민감한 정보 포함 여부
            
        Returns:
            설정 정보 딕셔너리
        """
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'symbol': self.symbol,
            'leverage': self.leverage,
            'investment_ratio': self.investment_ratio,
            'is_demo': self.is_demo,
            'has_demo_api': self.has_demo_api(),
            'has_real_api': self.has_real_api(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_private:
            if self.has_demo_api():
                result.update({
                    'demo_api_key': self.demo_api_key[:4] + '****' + self.demo_api_key[-4:] if self.demo_api_key else None,
                    'demo_api_secret': self.demo_api_secret[:4] + '****' + self.demo_api_secret[-4:] if self.demo_api_secret else None
                })
            
            if self.has_real_api():
                result.update({
                    'real_api_key': self.real_api_key[:4] + '****' + self.real_api_key[-4:] if self.real_api_key else None,
                    'real_api_secret': self.real_api_secret[:4] + '****' + self.real_api_secret[-4:] if self.real_api_secret else None
                })
        
        return result
    
    def __repr__(self):
        return f"<Setting(id={self.id}, user_id={self.user_id}, symbol='{self.symbol}', is_demo={self.is_demo})>"