import requests
import time
import hmac
import hashlib
import os
import json
import websocket
import threading
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(os.path.join('backend', '.env'))

API_KEY = os.getenv("BYBIT_API_KEY")
API_SECRET = os.getenv("BYBIT_SECRET_KEY") 
BASE_URL = os.getenv("BASE_URL", "https://api-demo.bybit.com")

def generate_signature(api_secret, timestamp, api_key, recv_window, query_string):
    """Generate signature for Bybit API v5 authentication"""
    # Bybit API v5 signature format: timestamp + api_key + recv_window + query_string
    param_str = str(timestamp) + str(api_key) + str(recv_window) + str(query_string)
    
    print(f"🔐 서명 생성 중...")
    print(f"   Timestamp: {timestamp}")
    print(f"   API Key: {api_key}")
    print(f"   Recv Window: {recv_window}")
    print(f"   Query String: '{query_string}'")
    print(f"   Param String: '{param_str}'")
    
    signature = hmac.new(
        bytes(api_secret, "utf-8"), 
        param_str.encode("utf-8"), 
        hashlib.sha256
    ).hexdigest()
    
    print(f"   Generated Signature: {signature}")
    return signature

def get_wallet_balance():
    """Get wallet balance using Bybit API v5"""
    if not API_KEY or not API_SECRET or API_KEY == "YOUR_NEW_API_KEY_HERE":
        print("❌ Error: API_KEY and API_SECRET must be set in .env file")
        print("\n📋 To get your API keys:")
        print("1. Login to Bybit mainnet (https://www.bybit.com)")
        print("2. Switch to 'Demo Trading' mode")
        print("3. Hover over user avatar and click 'API'")
        print("4. Create new API key with Wallet read permissions")
        print("5. Update the .env file with your new keys")
        return
    
    # Validate API key format
    if len(API_KEY) < 20:
        print("⚠️  Warning: API_KEY seems too short. Bybit API keys are usually longer.")
        print("Please check if you have the correct API key from Bybit mainnet demo trading.")
    
    if len(API_SECRET) < 30:
        print("⚠️  Warning: API_SECRET seems too short. Bybit API secrets are usually longer.")
        print("Please check if you have the correct API secret from Bybit mainnet demo trading.")

    # API endpoint
    endpoint = "/v5/account/wallet-balance"
    
    # Required parameters
    timestamp = str(int(time.time() * 1000))
    recv_window = "5000"
    
    # Query parameters - accountType is required for wallet balance
    query_params = "accountType=UNIFIED"
    
    # Generate signature
    signature = generate_signature(API_SECRET, timestamp, API_KEY, recv_window, query_params)
    
    # Headers (Bybit v5 API format)
    headers = {
        "X-BAPI-API-KEY": API_KEY,
        "X-BAPI-SIGN": signature,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-RECV-WINDOW": recv_window,
        "Content-Type": "application/json"
    }
    
    # Make request
    url = f"{BASE_URL}{endpoint}?{query_params}"
    
    try:
        print(f"Making request to: {url}")
        print(f"Headers: {headers}")
        
        response = requests.get(url, headers=headers)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        data = response.json()
        print(f"Response data: {data}")
        
        if response.status_code == 200:
            if data.get("retCode") == 0:
                print("\n✅ Success! Wallet Balance:")
                result = data.get("result", {})
                
                # Print account info
                accounts = result.get("list", [])
                for account in accounts:
                    account_type = account.get("accountType", "Unknown")
                    print(f"\nAccount Type: {account_type}")
                    
                    coins = account.get("coin", [])
                    if coins:
                        print("Coins:")
                        for coin in coins:
                            coin_name = coin.get("coin", "Unknown")
                            wallet_balance = coin.get("walletBalance", "0")
                            available_balance = coin.get("availableToWithdraw", "0")
                            print(f"  {coin_name}: Wallet={wallet_balance}, Available={available_balance}")
                    else:
                        print("  No coins found")
            else:
                print(f"❌ API Error: {data.get('retMsg', 'Unknown error')}")
                print(f"Error Code: {data.get('retCode')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {data}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def get_position_info():
    """Get position information and P&L using Bybit API v5"""
    if not API_KEY or not API_SECRET or API_KEY == "YOUR_NEW_API_KEY_HERE":
        print("❌ Error: API_KEY and API_SECRET must be set in .env file")
        return

    # API endpoint for positions
    endpoint = "/v5/position/list"
    
    # Required parameters
    timestamp = str(int(time.time() * 1000))
    recv_window = "5000"
    
    # Query parameters - category is required
    query_params = "category=linear"  # For USDT perpetual contracts
    
    # Generate signature
    signature = generate_signature(API_SECRET, timestamp, API_KEY, recv_window, query_params)
    
    # Headers (Bybit v5 API format)
    headers = {
        "X-BAPI-API-KEY": API_KEY,
        "X-BAPI-SIGN": signature,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-RECV-WINDOW": recv_window,
        "Content-Type": "application/json"
    }
    
    # Make request
    url = f"{BASE_URL}{endpoint}?{query_params}"
    
    try:
        response = requests.get(url, headers=headers)
        data = response.json()
        
        if response.status_code == 200:
            if data.get("retCode") == 0:
                print("\n📊 Position Information:")
                result = data.get("result", {})
                positions = result.get("list", [])
                
                if positions:
                    total_unrealized_pnl = 0
                    for position in positions:
                        symbol = position.get("symbol", "Unknown")
                        side = position.get("side", "Unknown")
                        size = position.get("size", "0")
                        entry_price = position.get("avgPrice", "0")
                        mark_price = position.get("markPrice", "0")
                        unrealized_pnl = position.get("unrealisedPnl", "0")
                        percentage = position.get("unrealisedPnlPercentage", "0")
                        
                        if float(size) > 0:  # Only show active positions
                            print(f"\n🎯 Symbol: {symbol}")
                            print(f"   Side: {side}")
                            print(f"   Size: {size}")
                            print(f"   Entry Price: {entry_price}")
                            print(f"   Mark Price: {mark_price}")
                            print(f"   Unrealized P&L: {unrealized_pnl} ({percentage}%)")
                            
                            try:
                                total_unrealized_pnl += float(unrealized_pnl)
                            except ValueError:
                                pass
                    
                    if total_unrealized_pnl != 0:
                        print(f"\n💰 Total Unrealized P&L: {total_unrealized_pnl:.4f} USDT")
                    else:
                        print("\n📝 No active positions found")
                else:
                    print("📝 No positions found")
            else:
                print(f"❌ API Error: {data.get('retMsg', 'Unknown error')}")
                print(f"Error Code: {data.get('retCode')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {data}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def get_account_pnl():
    """Get account P&L history"""
    if not API_KEY or not API_SECRET or API_KEY == "YOUR_NEW_API_KEY_HERE":
        print("❌ Error: API_KEY and API_SECRET must be set in .env file")
        return

    # API endpoint for P&L
    endpoint = "/v5/position/closed-pnl"
    
    # Required parameters
    timestamp = str(int(time.time() * 1000))
    recv_window = "5000"
    
    # Query parameters
    query_params = "category=linear&limit=10"  # Last 10 closed positions
    
    # Generate signature
    signature = generate_signature(API_SECRET, timestamp, API_KEY, recv_window, query_params)
    
    # Headers (Bybit v5 API format)
    headers = {
        "X-BAPI-API-KEY": API_KEY,
        "X-BAPI-SIGN": signature,
        "X-BAPI-TIMESTAMP": timestamp,
        "X-BAPI-RECV-WINDOW": recv_window,
        "Content-Type": "application/json"
    }
    
    # Make request
    url = f"{BASE_URL}{endpoint}?{query_params}"
    
    try:
        response = requests.get(url, headers=headers)
        data = response.json()
        
        if response.status_code == 200:
            if data.get("retCode") == 0:
                print("\n📈 Recent Closed Positions P&L:")
                result = data.get("result", {})
                pnl_list = result.get("list", [])
                
                if pnl_list:
                    total_realized_pnl = 0
                    for pnl in pnl_list:
                        symbol = pnl.get("symbol", "Unknown")
                        side = pnl.get("side", "Unknown")
                        qty = pnl.get("qty", "0")
                        closed_pnl = pnl.get("closedPnl", "0")
                        created_time = pnl.get("createdTime", "0")
                        
                        # Convert timestamp to readable date
                        if created_time and created_time != "0":
                            date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(created_time)/1000))
                        else:
                            date = "Unknown"
                        
                        print(f"\n📊 {symbol} ({side})")
                        print(f"   Quantity: {qty}")
                        print(f"   Realized P&L: {closed_pnl} USDT")
                        print(f"   Closed: {date}")
                        
                        try:
                            total_realized_pnl += float(closed_pnl)
                        except ValueError:
                            pass
                    
                    print(f"\n💰 Total Recent Realized P&L: {total_realized_pnl:.4f} USDT")
                else:
                    print("📝 No closed positions found")
            else:
                print(f"❌ API Error: {data.get('retMsg', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def start_pnl_monitoring():
    """Start P&L monitoring every minute"""
    if not API_KEY or not API_SECRET or API_KEY == "YOUR_NEW_API_KEY_HERE":
        print("❌ Error: API_KEY and API_SECRET must be set in .env file")
        return
    
    print("🚀 Starting P&L monitoring (1-minute intervals)...")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            print(f"\n⏰ {time.strftime('%Y-%m-%d %H:%M:%S')} - Checking P&L...")
            print("=" * 50)
            get_wallet_balance()
            get_position_info()
            get_account_pnl()
            print("=" * 50)
            print("💤 Waiting 60 seconds...")
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n🛑 Stopping P&L monitoring...")

if __name__ == "__main__":
    print("🚀 Starting Bybit Trading Monitor...")
    
    # Ask user for preferred method
    print("\nSelect monitoring option:")
    print("1. One-time balance check")
    print("2. One-time position & P&L check")
    print("3. Complete monitoring (Balance + P&L) every minute")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        get_wallet_balance()
    elif choice == "2":
        get_wallet_balance()
        get_position_info()
        get_account_pnl()
    elif choice == "3":
        start_pnl_monitoring()
    else:
        print("Invalid choice. Running complete check...")
        get_wallet_balance()
        get_position_info()
        get_account_pnl()()
