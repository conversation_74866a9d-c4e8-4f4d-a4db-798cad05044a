"""
SignalR 클라이언트 - 실제 ASP.NET Core SignalR 서버 연결
"""
import logging
import time
from datetime import datetime
import queue
from signalrcore.hub_connection_builder import HubConnectionBuilder

logger = logging.getLogger(__name__)

class SignalRClient:
    def __init__(self, url="http://localhost:5000/signalr", log_only=False):
        self.url = url
        self.hub_connection = None
        self.is_connected = False
        self.signal_queue = queue.Queue()
        self.last_processed_signal = None  # 중복 필터링용
        self.log_only = log_only  # 로그 전용 모드 (매매 실행 없음)

    def start(self):
        """실제 ASP.NET Core SignalR 서버에 연결"""
        logger.info("SignalR start() 메서드가 호출되었습니다!")
        try:
            if self.is_connected:
                logger.warning("이미 연결되어 있습니다.")
                return True

            logger.info(f"SignalR 연결 시도 중... URL: {self.url}")

            # 실제 SignalR 서버에 연결 (간단한 설정)
            self.hub_connection = HubConnectionBuilder() \
                .with_url(self.url) \
                .build()

            # 신호 수신 핸들러 등록
            self.hub_connection.on("SendSignal", self._handle_signal)
            self.hub_connection.on_open(self._on_open)
            self.hub_connection.on_close(self._on_close)
            self.hub_connection.on_error(self._on_error)

            logger.info("SignalR 핸들러 등록 완료")

            # 연결 시작
            logger.info("SignalR 연결 시작 중...")
            self.hub_connection.start()

            # 연결 후 잠시 대기
            import time
            time.sleep(1)

            self.is_connected = True

            logger.info("SignalR 연결 성공!")
            logger.info("'SendSignal' 메서드 수신 대기 중...")
            logger.info(f"연결 URL: {self.url}")
            logger.info("실제 C# 검출기 앱의 신호를 기다리는 중...")

            return True

        except Exception as e:
            logger.error(f"SignalR 연결 오류: {e}")
            logger.error(f"연결 URL: {self.url}")
            logger.error(f"오류 타입: {type(e).__name__}")
            self.is_connected = False
            return False

    def _on_open(self):
        """SignalR 연결 열림 이벤트"""
        logger.info("SignalR 연결이 열렸습니다!")
        self.is_connected = True

    def _on_close(self):
        """SignalR 연결 닫힘 이벤트"""
        logger.info("SignalR 연결이 닫혔습니다")
        self.is_connected = False

    def _on_error(self, data):
        """SignalR 연결 오류 이벤트"""
        logger.error(f"SignalR 연결 오류: {data}")
        self.is_connected = False

    def _handle_signal(self, message):
        """SignalR 신호 수신 핸들러 - 텍스트 기반 + 중복 필터링"""
        try:
            logger.info(f"SignalR 신호 수신 (log_only={self.log_only}): {message}")

            # 1. 텍스트 기반 신호 필터링
            if "롱 신호가 감지되었습니다!" in str(message):
                signal_type = "LONG"
            elif "숏 신호가 감지되었습니다!" in str(message):
                signal_type = "SHORT"
            else:
                logger.debug(f"필터링된 신호: {message}")
                return  # 지정된 형태가 아닌 신호 무시

            # 2. 중복 신호 필터링
            if self.last_processed_signal == signal_type:
                logger.info(f"중복 신호 무시: {signal_type}")
                return

            # 3. 새로운 신호 처리
            self.last_processed_signal = signal_type

            # 4. 로그 전용 모드와 매매 실행 모드 구분
            if self.log_only:
                # 로그 전용 모드: 매매 액션 없이 로그용 데이터만 생성
                signal_data = {
                    'timestamp': datetime.now().strftime("%H:%M:%S"),
                    'message': str(message),
                    'signal_type': signal_type,
                    'action': 'LOG_ONLY',  # 🟢 로그 전용 액션
                    'received_at': datetime.now().isoformat(),
                    'log_only': True
                }
                logger.info(f"로그 전용 신호 수신: {signal_type} (매매 실행 없음)")
            else:
                # 매매 실행 모드: 단방향 진입으로 변경
                signal_data = {
                    'timestamp': datetime.now().strftime("%H:%M:%S"),
                    'message': str(message),
                    'signal_type': signal_type,
                    'action': 'IMMEDIATE_UNIDIRECTIONAL_ENTRY',  # 🟢 단방향 매매 실행 액션
                    'received_at': datetime.now().isoformat(),
                    'log_only': False
                }
                logger.info(f"매매 실행 신호 큐에 추가: {signal_type}")

            self.signal_queue.put(signal_data)

        except Exception as e:
            logger.error(f"신호 처리 오류: {e}")
            logger.error(f"신호 데이터: {message}")

    def _send_test_message(self):
        """테스트 메시지 전송"""
        try:
            if self.hub_connection:
                test_message = f"테스트 신호 - {datetime.now().strftime('%H:%M:%S')}"
                logger.info(f"테스트 메시지 전송 중: {test_message}")

                # SignalR 서버에 테스트 메시지 전송 (리스트 형태로)
                self.hub_connection.send("SendSignal", [test_message])
                logger.info("테스트 메시지 전송 완료")

        except Exception as e:
            logger.error(f"테스트 메시지 전송 오류: {e}")

    def stop(self):
        """SignalR 연결 중지"""
        try:
            if self.hub_connection:
                self.hub_connection.stop()
            self.is_connected = False
            logger.info("SignalR 연결 중지")
        except Exception as e:
            logger.error(f"SignalR 연결 중지 오류: {e}")



    def clear_signal_queue(self):
        """신호 큐 초기화"""
        try:
            while not self.signal_queue.empty():
                self.signal_queue.get_nowait()
            logger.info("신호 큐가 초기화되었습니다")
        except Exception as e:
            logger.error(f"신호 큐 초기화 오류: {e}")

    def get_queue_size(self):
        """큐 크기 조회"""
        return self.signal_queue.qsize()

    def is_connected_to_signalr(self):
        """SignalR 연결 상태 확인"""
        return self.is_connected and self.websocket is not None

    def reconnect(self):
        """재연결 시도"""
        try:
            logger.info("SignalR 재연결 시도")
            self.stop()
            time.sleep(2)  # 잠시 대기
            return self.start()
        except Exception as e:
            logger.error(f"재연결 오류: {e}")
            return False

    def get_detailed_status(self):
        """상세 상태 정보 조회"""
        return {
            'is_connected': self.is_connected,
            'websocket_active': self.websocket is not None,
            'url': self.url,
            'queue_size': self.signal_queue.qsize(),
            'connection_thread_alive': self.connection_thread.is_alive() if self.connection_thread else False
        }
            
    def get_latest_signals(self, max_signals=10):
        """최신 시그널 조회"""
        signals = []
        
        try:
            while not self.signal_queue.empty() and len(signals) < max_signals:
                signals.append(self.signal_queue.get_nowait())
                
        except queue.Empty:
            pass
            
        return signals
        
    def send_message(self, target, *args):
        """메시지 전송"""
        if not self.is_connected or not self.hub_connection:
            logger.warning("연결되지 않음")
            return False

        try:
            self.hub_connection.send(target, list(args))
            return True

        except Exception as e:
            logger.error(f"메시지 전송 오류: {e}")
            return False
            
    def get_connection_status(self):
        """연결 상태 반환"""
        return {
            'is_connected': self.is_connected,
            'url': self.url,
            'queue_size': self.signal_queue.qsize()
        }
