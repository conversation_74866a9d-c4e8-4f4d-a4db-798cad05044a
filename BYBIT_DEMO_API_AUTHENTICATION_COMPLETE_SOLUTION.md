# Bybit Demo API 인증 해결 완전 가이드

## 📋 목차
1. [문제 개요](#문제-개요)
2. [초기 진단](#초기-진단)
3. [근본 원인 분석](#근본-원인-분석)
4. [해결 과정](#해결-과정)
5. [최종 검증](#최종-검증)
6. [기술적 세부사항](#기술적-세부사항)
7. [향후 가이드라인](#향후-가이드라인)

---

## 🚨 문제 개요

### 증상
- **오류 코드**: 10003 "API key is invalid"
- **영향 범위**: 모든 인증이 필요한 API 엔드포인트
- **정상 기능**: 공개 API (시세, 심볼 정보)만 작동
- **환경**: Bybit Demo Trading (`https://api-demo.bybit.com`)

### 비즈니스 임팩트
- ❌ 자동화된 거래 봇 개발 중단
- ❌ 계정 정보, 잔고, 포지션 조회 불가
- ❌ Demo 모드에서 거래 로직 테스트 불가능

---

## 🔍 초기 진단

### 1. 환경 설정 확인
```env
# backend/.env 파일
BYBIT_API_KEY=u3ZXadFktdLqQ95DZx
BYBIT_SECRET_KEY=1vrTxh3PaEJiVhZp8RTPz2CUp7bZ0fwnRXfi
BASE_URL=https://api-demo.bybit.com
```

### 2. API 키 유효성 검사
- ✅ **키 길이**: 적절한 길이의 API 키/시크릿
- ✅ **Demo 엔드포인트**: 올바른 Demo API URL
- ✅ **키 출처**: Bybit 공식 Demo Trading에서 발급된 키

### 3. 초기 테스트 결과
```
Response Status: 401 Unauthorized
Error: 10003 "API key is invalid"
```

---

## 🎯 근본 원인 분석

### 공식 문서 분석 결과

#### Bybit V5 API 인증 요구사항
- **공식 문서**: `https://bybit-exchange.github.io/docs/v5/guide`
- **핵심 발견**: 헤더 형식이 V5에서 변경됨

#### 잘못된 헤더 형식 (기존)
```python
headers = {
    "X-BYBIT-API-KEY": API_KEY,      # ❌ 잘못된 형식
    "X-BYBIT-SIGN": signature,       # ❌ 잘못된 형식
    "X-BYBIT-TS": timestamp,         # ❌ 잘못된 형식
    "X-BYBIT-RECV-WINDOW": recv_window  # ❌ 잘못된 형식
}
```

#### 올바른 헤더 형식 (V5)
```python
headers = {
    "X-BAPI-API-KEY": API_KEY,       # ✅ 올바른 형식
    "X-BAPI-SIGN": signature,        # ✅ 올바른 형식
    "X-BAPI-TIMESTAMP": timestamp,   # ✅ 올바른 형식
    "X-BAPI-RECV-WINDOW": recv_window  # ✅ 올바른 형식
}
```

### 추가 발견사항
1. **환경 변수 매핑**: `API_KEY` → `BYBIT_API_KEY` 불일치
2. **서명 생성**: 알고리즘은 정확했으나 헤더 적용 오류
3. **Demo 키 유효성**: API 키 자체는 완전히 정상

---

## 🔧 해결 과정

### 단계 1: 공식 문서 확인
```bash
# Bybit V5 Demo API 공식 문서 분석
https://bybit-exchange.github.io/docs/v5/demo
https://bybit-exchange.github.io/docs/v5/guide
```

**핵심 확인사항:**
- Demo API 키 생성 방법
- V5 API 헤더 형식
- 서명 생성 방식

### 단계 2: 환경 변수 수정
```python
# 파일: demo_check_balance.py
# 수정 전
load_dotenv()
API_KEY = os.getenv("API_KEY")
API_SECRET = os.getenv("API_SECRET")

# 수정 후
load_dotenv(os.path.join('backend', '.env'))
API_KEY = os.getenv("BYBIT_API_KEY")
API_SECRET = os.getenv("BYBIT_SECRET_KEY")
```

### 단계 3: API 헤더 형식 수정
```python
# 모든 API 호출에서 헤더 수정
# 1. get_wallet_balance() 함수
# 2. get_position_info() 함수  
# 3. get_closed_pnl() 함수

# 수정 내용 (3곳 모두 동일)
headers = {
    "X-BAPI-API-KEY": API_KEY,        # X-BYBIT-API-KEY → X-BAPI-API-KEY
    "X-BAPI-SIGN": signature,         # X-BYBIT-SIGN → X-BAPI-SIGN
    "X-BAPI-TIMESTAMP": timestamp,    # X-BYBIT-TS → X-BAPI-TIMESTAMP
    "X-BAPI-RECV-WINDOW": recv_window, # X-BYBIT-RECV-WINDOW → X-BAPI-RECV-WINDOW
    "Content-Type": "application/json"
}
```

### 단계 4: 서명 생성 검증
```python
def generate_signature(api_secret, timestamp, api_key, recv_window, query_string):
    """Generate signature for Bybit API v5 authentication"""
    param_str = str(timestamp) + str(api_key) + str(recv_window) + str(query_string)
    
    signature = hmac.new(
        bytes(api_secret, "utf-8"), 
        param_str.encode("utf-8"), 
        hashlib.sha256
    ).hexdigest()
    
    return signature
```

---

## ✅ 최종 검증

### 테스트 실행
```bash
python demo_check_balance.py
```

### 성공 결과
```json
{
  "retCode": 0,
  "retMsg": "OK",
  "result": {
    "list": [{
      "totalEquity": "205984.********",
      "accountType": "UNIFIED",
      "totalWalletBalance": "98999.********",
      "totalAvailableBalance": "98894.********",
      "coin": [
        {
          "coin": "USDC",
          "walletBalance": "50000",
          "usdValue": "49998.6"
        },
        {
          "coin": "BTC", 
          "walletBalance": "1",
          "usdValue": "104464.923332"
        },
        {
          "coin": "ETH",
          "walletBalance": "1", 
          "usdValue": "2519.424894"
        },
        {
          "coin": "USDT",
          "walletBalance": "48991.********",
          "usdValue": "49001.********"
        }
      ]
    }]
  }
}
```

### 검증 포인트
- ✅ **HTTP Status**: 200 OK
- ✅ **retCode**: 0 (성공)
- ✅ **계정 유형**: UNIFIED
- ✅ **총 자산**: $205,984.07
- ✅ **API 제한**: X-Bapi-Limit 헤더 확인

---

## 🛠 기술적 세부사항

### Bybit V5 API 인증 플로우
```mermaid
graph TD
    A[API 요청 시작] --> B[타임스탬프 생성]
    B --> C[쿼리 스트링 구성]
    C --> D[서명 문자열 생성: timestamp + api_key + recv_window + query_string]
    D --> E[HMAC-SHA256 서명 생성]
    E --> F[X-BAPI-* 헤더 설정]
    F --> G[API 요청 전송]
    G --> H{응답 확인}
    H -->|200 OK| I[성공]
    H -->|401| J[인증 실패]
    H -->|403| K[권한 없음]
```

### 서명 생성 상세
```python
# 서명 생성 파라미터 순서 (중요!)
param_str = timestamp + api_key + recv_window + query_string

# 예시
timestamp = "*************"
api_key = "u3ZXadFktdLqQ95DZx" 
recv_window = "5000"
query_string = "accountType=UNIFIED"

# 결합된 문자열
param_str = "*************u3ZXadFktdLqQ95DZx5000accountType=UNIFIED"

# HMAC-SHA256 서명
signature = hmac.new(
    bytes(api_secret, "utf-8"),
    param_str.encode("utf-8"),
    hashlib.sha256
).hexdigest()
```

### 헤더 비교표
| 구분 | V4/이전 형식 | V5 형식 | 상태 |
|------|-------------|---------|------|
| API 키 | X-BYBIT-API-KEY | X-BAPI-API-KEY | ✅ 수정됨 |
| 서명 | X-BYBIT-SIGN | X-BAPI-SIGN | ✅ 수정됨 |
| 타임스탬프 | X-BYBIT-TS | X-BAPI-TIMESTAMP | ✅ 수정됨 |
| 수신 윈도우 | X-BYBIT-RECV-WINDOW | X-BAPI-RECV-WINDOW | ✅ 수정됨 |

---

## 📚 향후 가이드라인

### 1. 개발 환경 설정
```bash
# 환경 변수 검증 스크립트
python -c "
import os
from dotenv import load_dotenv
load_dotenv('backend/.env')
print('API_KEY:', os.getenv('BYBIT_API_KEY')[:10] + '...')
print('SECRET:', '*' * 20)
print('URL:', os.getenv('BASE_URL'))
"
```

### 2. API 클라이언트 표준 구현
```python
class BybitV5Client:
    def __init__(self, api_key, api_secret, base_url):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = base_url
    
    def _generate_signature(self, timestamp, recv_window, query_string):
        param_str = f"{timestamp}{self.api_key}{recv_window}{query_string}"
        return hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, endpoint, params=None):
        timestamp = str(int(time.time() * 1000))
        recv_window = "5000"
        query_string = urllib.parse.urlencode(params or {})
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": self._generate_signature(timestamp, recv_window, query_string),
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": recv_window,
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{self.base_url}{endpoint}", 
                              params=params, headers=headers)
        return response.json()
```

### 3. 에러 처리 가이드
```python
def handle_bybit_response(response_data):
    ret_code = response_data.get('retCode', -1)
    ret_msg = response_data.get('retMsg', 'Unknown error')
    
    if ret_code == 0:
        return response_data.get('result')
    elif ret_code == 10003:
        raise AuthenticationError("Invalid API key or signature")
    elif ret_code == 10004:
        raise AuthenticationError("Invalid API key permissions")
    elif ret_code == 10005:
        raise AuthenticationError("Permission denied")
    else:
        raise APIError(f"API Error {ret_code}: {ret_msg}")
```

### 4. 모니터링 및 로깅
```python
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def log_api_call(endpoint, response_time, status_code):
    logger.info(f"API Call: {endpoint} | "
                f"Response Time: {response_time}ms | "
                f"Status: {status_code}")
```

### 5. 테스트 체크리스트
- [ ] 환경 변수 로드 확인
- [ ] API 키 유효성 검증
- [ ] 서명 생성 테스트
- [ ] 헤더 형식 검증
- [ ] 타임스탬프 동기화 확인
- [ ] 레이트 리미트 처리
- [ ] 에러 응답 핸들링

---

## 🎯 결론

### 해결 요약
1. **근본 원인**: Bybit V5 API 헤더 형식 변경 미적용
2. **핵심 수정**: `X-BYBIT-*` → `X-BAPI-*` 헤더 형식 변경
3. **부차적 수정**: 환경 변수 매핑 정정
4. **검증 완료**: Demo API 키 정상 작동 확인

### 비즈니스 가치
- ✅ **개발 환경 복구**: Demo 모드에서 안전한 개발 재개
- ✅ **리스크 관리**: 실제 자금 없이 거래 로직 검증
- ✅ **확장성 확보**: 동일한 방식으로 실서버 연결 가능
- ✅ **개발 효율성**: 신뢰할 수 있는 API 연동 기반 구축

### 향후 계획
1. **Phase 1**: Demo 환경에서 전체 봇 기능 구현 및 테스트
2. **Phase 2**: 백테스팅 결과와 Demo 거래 결과 비교 분석
3. **Phase 3**: 실서버 환경으로 점진적 이전 및 라이브 거래

---

**문서 작성일**: 2025년 6월 20일  
**문서 버전**: 1.0  
**작성자**: AI Assistant  
**검토 상태**: ✅ 완료
