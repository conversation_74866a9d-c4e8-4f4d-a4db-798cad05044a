🏗️ 시스템 아키텍처
mermaidgraph TB
    subgraph "🎯 외부 신호"
        SIGNALR[SignalR Server<br/>Port: 5000<br/>13:45:46 롱 신호<br/>13:45:50 숏 신호]
    end
    
    subgraph "🐍 Python 백엔드 (Port: 8000)"
        SIGNAL_CLIENT[SignalR Client<br/>신호 수신기]
        PROCESSOR[Signal Processor<br/>동적 전환 로직]
        TRADING[Trading Service<br/>PyBit v5 자동매매]
        WS_MANAGER[WebSocket Manager<br/>실시간 통신]
        AUTH[Auth Service<br/>JWT 멀티유저]
        REDIS[(Redis Cache<br/>사용자/세션/상태)]
    end
    
    subgraph "⚛️ React 프론트엔드 (Port: 3000)"
        UI[TradingPlatform.jsx<br/>단일 파일 UI]
        CHARTS[실시간 모니터링<br/>포지션/차트]
        CONTROLS[봇 제어 패널<br/>설정 관리]
    end
    
    subgraph "🌐 외부 시스템"
        BYBIT[Bybit API v5<br/>실제 거래]
        EXTERNAL[외부 웹 접근<br/>데스크탑 → 인터넷]
    end
    
    SIGNALR -->|실시간 신호| SIGNAL_CLIENT
    SIGNAL_CLIENT -->|신호 파싱| PROCESSOR
    PROCESSOR -->|거래 명령| TRADING
    TRADING -->|API 호출| BYBIT
    PROCESSOR -->|상태 업데이트| REDIS
    WS_MANAGER -->|실시간 데이터| UI
    AUTH -->|사용자 관리| REDIS
    EXTERNAL -->|HTTP/WebSocket| UI
🎮 핵심 운영 규칙 (동적 양방향 전환 전략)
1️⃣ 포지션 운영 방식

✅ 최초 신호: 양방향 진입 (롱 50% + 숏 50%)
✅ 방향 결정: 한쪽이 -0.5% 손실 시 → 반대 방향 100%
✅ 지속 모니터링: 단방향 운영 중에도 -0.5% 손실 시 방향 전환
✅ 신호 수신: 기존 포지션 청산 → 새로운 양방향 진입

2️⃣ 동적 방향 전환 워크플로우
mermaidflowchart TD
    A[봇 시작] --> B{신호 검출?}
    B -->|No| B
    B -->|Yes| C{기존 포지션?}
    
    C -->|있음| D[전체 청산]
    C -->|없음| E[양방향 진입 준비]
    D --> E
    
    E --> F[자산 비율 적용<br/>25%/50%/75%/100%]
    F --> G[양방향 주문<br/>롱: +0.5% / 숏: -0.5%<br/>각 50%씩]
    
    G --> H{체결 상태}
    H -->|양방향 체결| I[양방향 모니터링]
    H -->|단방향 체결| J[단방향 모니터링]
    
    I --> K{손익 확인}
    K -->|롱 -0.5%| L1[롱 청산 → 숏 100%]
    K -->|숏 -0.5%| L2[숏 청산 → 롱 100%]
    K -->|둘 다 수익| I
    
    L1 --> M[숏 단방향 운영]
    L2 --> N[롱 단방향 운영]
    J --> O{현재 방향?}
    O -->|롱| N
    O -->|숏| M
    
    M --> P{숏 손익 확인}
    P -->|수익 or 0~-0.5%| M
    P -->|-0.5% 도달| Q[숏 청산 → 롱 전환]
    
    N --> R{롱 손익 확인}
    R -->|수익 or 0~-0.5%| N
    R -->|-0.5% 도달| S[롱 청산 → 숏 전환]
    
    Q --> N
    S --> M
    
    M --> T{새 신호?}
    N --> T
    T -->|Yes| D
    T -->|No| U{현재 방향?}
    U -->|롱| N
    U -->|숏| M
3️⃣ 방향 전환 메커니즘
양방향 → 단방향 (최초 전환)
pythonif 포지션상태 == "양방향":
    if 롱손익률 <= -0.5:
        롱포지션_시장가_청산()
        청산금액 = 롱청산금 + 기존숏금액
        숏포지션_추가진입(청산금액)
        포지션상태 = "단방향_숏"
    
    elif 숏손익률 <= -0.5:
        숏포지션_시장가_청산()
        청산금액 = 숏청산금 + 기존롱금액
        롱포지션_추가진입(청산금액)
        포지션상태 = "단방향_롱"
단방향 → 반대방향 (동적 전환)
pythonif 포지션상태 == "단방향_롱":
    if 롱손익률 <= -0.5:
        롱포지션_시장가_청산()
        숏포지션_신규진입(청산금액, 현재가)
        포지션상태 = "단방향_숏"
        
elif 포지션상태 == "단방향_숏":
    if 숏손익률 <= -0.5:
        숏포지션_시장가_청산()
        롱포지션_신규진입(청산금액, 현재가)
        포지션상태 = "단방향_롱"