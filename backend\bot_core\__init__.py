# Bot Core Init
# bot_core/__init__.py
import logging
from threading import Thread, Event
from queue import Queue, Empty
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from api_client.signalr_client import SignalRClient
from bot_core.trading_logic import TradingLogic

logger = logging.getLogger("opensystems_bot")

class SignalProcessor:
    """색상 검출 신호 처리기"""

    def __init__(self, user_id: int, trading_logic: TradingLogic, signalr_url: str = "http://localhost:5000/signalr"):
        self.user_id = user_id
        self.trading_logic = trading_logic
        # 🔴 매매 실행용 SignalR 클라이언트 (Start Bot 후에만 활성화)
        self.signalr_client = SignalRClient(signalr_url, log_only=False)

        # 처리 상태
        self.is_running = False
        self.processing_thread = None
        self.stop_event = Event()

        # 통계
        self.stats = {
            'signals_received': 0,
            'signals_processed': 0,
            'trades_executed': 0,
            'last_signal_time': None,
            'processing_errors': 0
        }

        logger.info(f"SignalProcessor 초기화 - 사용자: {user_id}")

    def start(self):
        """신호 처리 시작"""
        if self.is_running:
            logger.warning("SignalProcessor가 이미 실행 중입니다")
            return False

        try:
            # SignalR 연결 시작
            self.signalr_client.start()

            # 처리 스레드 시작
            self.is_running = True
            self.stop_event.clear()
            self.processing_thread = Thread(target=self._process_signals_loop, daemon=True)
            self.processing_thread.start()

            # 포지션 모니터링 시작
            if self.trading_logic:
                # 🚨 중요: TradingLogic 봇 시작
                self.trading_logic.start_bot()
                self.trading_logic.start_position_monitoring()

            logger.info(f"SignalProcessor 시작됨 - 사용자: {self.user_id}")
            return True

        except Exception as e:
            logger.error(f"SignalProcessor 시작 오류: {e}")
            self.is_running = False
            return False

    def stop(self):
        """신호 처리 중지 (모든 포지션 청산 후 중지)"""
        if not self.is_running:
            return

        try:
            logger.info(f"Stop Bot 요청 - 모든 포지션 청산 시작 (사용자: {self.user_id})")

            # 1단계: 모든 포지션 청산 (stop_bot_cleanup 함수 사용)
            if self.trading_logic:
                # 🚨 중요: TradingLogic 봇 중지
                self.trading_logic.stop_bot()
                self.trading_logic.stop_bot_cleanup()

            # 2단계: 중지 신호 설정
            self.is_running = False
            self.stop_event.set()

            # 3단계: 포지션 모니터링 중지
            if self.trading_logic:
                self.trading_logic.stop_position_monitoring()

            # 4단계: SignalR 연결 중지
            self.signalr_client.stop()

            # 5단계: 처리 스레드 종료 대기
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5)

            logger.info(f"모든 포지션 청산 완료 - 봇 완전 중지됨 (사용자: {self.user_id})")

        except Exception as e:
            logger.error(f"SignalProcessor 중지 오류: {e}")

    def _process_signals_loop(self):
        """신호 처리 루프 (별도 스레드에서 실행)"""
        logger.info("신호 처리 루프 시작")

        while self.is_running and not self.stop_event.is_set():
            try:
                # SignalR에서 신호 가져오기
                signals = self.signalr_client.get_latest_signals(max_signals=5)

                for signal_data in signals:
                    if not self.is_running:
                        break

                    self._process_single_signal(signal_data)

                # 잠시 대기
                self.stop_event.wait(timeout=0.1)

            except Exception as e:
                logger.error(f"신호 처리 루프 오류: {e}")
                self.stats['processing_errors'] += 1
                self.stop_event.wait(timeout=1)

        logger.info("신호 처리 루프 종료")

    def _process_single_signal(self, signal_data: Dict[str, Any]):
        """단일 신호 처리"""
        try:
            self.stats['signals_received'] += 1
            self.stats['last_signal_time'] = datetime.now(timezone.utc).isoformat()

            logger.info(f"신호 처리 중: {signal_data}")

            # 봇 실행 상태 확인
            if not self._is_bot_active():
                logger.info("봇이 실행되지 않음 - 신호 처리 스킵")
                return

            # 매매 로직으로 신호 전달
            result = self.trading_logic.process_signal(signal_data)

            if result.get('processed'):
                self.stats['signals_processed'] += 1

                # 거래가 실행된 경우
                if result.get('action_taken') in ['BIDIRECTIONAL_ENTRY', 'STOP_LOSS_AND_FLIP', 'DIRECTION_CHANGE_TO_LONG', 'DIRECTION_CHANGE_TO_SHORT']:
                    self.stats['trades_executed'] += 1

                logger.info(f"신호 처리 완료: {result.get('message', '')}")
            else:
                logger.debug(f"신호 처리 스킵: {result.get('message', '')}")

        except Exception as e:
            logger.error(f"단일 신호 처리 오류: {e}")
            self.stats['processing_errors'] += 1

    def _is_bot_active(self) -> bool:
        """봇이 활성 상태인지 확인"""
        try:
            # active_bots 딕셔너리에서 현재 사용자 확인
            from routes.bot_routes import active_bots
            return self.user_id in active_bots
        except Exception as e:
            logger.error(f"봇 활성 상태 확인 오류: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """연결 상태 조회"""
        signalr_status = self.signalr_client.get_connection_status()

        return {
            'is_running': self.is_running,
            'signalr_connected': signalr_status.get('is_connected', False),
            'signalr_queue_size': signalr_status.get('queue_size', 0),
            'stats': self.stats.copy()
        }

    def get_stats(self) -> Dict[str, Any]:
        """처리 통계 조회"""
        return self.stats.copy()

    def reset_stats(self):
        """통계 초기화"""
        self.stats = {
            'signals_received': 0,
            'signals_processed': 0,
            'trades_executed': 0,
            'last_signal_time': None,
            'processing_errors': 0
        }
        logger.info("SignalProcessor 통계 초기화됨")

# 편의를 위한 import
from .trading_logic import TradingLogic, PositionType
from .mode_manager import ModeManager
