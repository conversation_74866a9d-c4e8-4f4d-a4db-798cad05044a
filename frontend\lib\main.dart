// lib/main.dart
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'dart:convert';
import 'package:signalr_core/signalr_core.dart';
import 'package:http/http.dart' as http;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Trading Platform',
      theme: ThemeData(
        brightness: Brightness.dark,
        scaffoldBackgroundColor: const Color(0xFF0D0D0D),
      ),
      home: const OpenSystemsBot0525(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class OpenSystemsBot0525 extends StatefulWidget {
  const OpenSystemsBot0525({Key? key}) : super(key: key);

  @override
  State<OpenSystemsBot0525> createState() => _OpenSystemsBot0525State();
}

class _OpenSystemsBot0525State extends State<OpenSystemsBot0525>
    with TickerProviderStateMixin {
  // State variables
  bool isLoggedIn = false;
  bool showSettings = false;
  bool showApiModal = false;
  bool isSignUp = false;

  // ✅ 추가: 현재 로그인한 사용자명 저장
  String? currentUsername;

  // 프로필 수정 관련 변수
  bool showProfileEdit = false;
  Map<String, String> profileForm = {
    'email': '',
    'current_password': '',
    'new_password': '',
    'confirm_password': '',
  };

  // ✅ 추가: 동적 API URL 설정
  String get apiBaseUrl {
    // 현재 웹앱이 실행 중인 호스트를 기반으로 API URL 결정
    final currentHost = Uri.base.host;
    if (currentHost == 'localhost' || currentHost == '127.0.0.1') {
      return 'http://localhost:3000';
    } else {
      return 'http://$currentHost:3000';
    }
  }

  // ✅ 추가: 동적 SignalR URL 설정
  String get signalRUrl {
    // 현재 웹앱이 실행 중인 호스트를 기반으로 SignalR URL 결정
    final currentHost = Uri.base.host;
    if (currentHost == 'localhost' || currentHost == '127.0.0.1') {
      return 'http://localhost:5000/signalr';
    } else {
      return 'http://$currentHost:5000/signalr';
    }
  }

  bool showPassword = false;
  bool showApiKey = false;
  bool showApiSecret = false;
  String activeTab = 'home';

  // Form data
  final loginForm = {'username': '', 'password': ''};
  final signUpForm = {
    'username': '',
    'email': '',
    'password': '',
    'confirmPassword': ''
  };
  final apiKeys = {'apiKey': '', 'apiSecret': ''};
  final settings = {
    'symbol': 'BTCUSDT',
    'leverage': '5X',
    'investmentRatio': '25%',
    'hedgingThreshold': '-1.0%' // 🟢 헷징 임계값 설정 추가
  };

  // Bot state
  bool isConnected = false;
  bool isBotRunning = false;
  String botStatus = 'IDLE';
  Map<String, dynamic>? currentPosition;
  Map<String, dynamic>? signalData;
  List<Map<String, dynamic>> signalHistory = [];

  // Status card state
  String? statusMessage;
  bool showStatusCard = false;
  bool isStatusSuccess = true;
  int secondarySignalCount = 0;
  DateTime? botStartTime;
  DateTime lastSyncTime = DateTime.now();
  double marketPrice = 45234.56;
  String logFilter = 'all';

  // Stats
  final stats = {
    'totalTrades': 0,
    'winRate': 0.0,
    'totalProfit': 0.0,
    'currentPnL': 0.0,
  };

  // 실시간 자산 정보
  double totalEquity = 0.0;
  double totalProfit = 0.0;
  String lastUpdateTime = '-';
  Timer? accountStatsTimer;

  // 실시간 로그 데이터 (SignalR에서 수신)
  List<Map<String, dynamic>> logs = [];

  // SignalR 연결 관련
  HubConnection? hubConnection;
  bool isSignalRConnected = false;

  // 신호 데이터 (SignalR에서 수신 예정)
  String? lastSignalType; // 마지막 신호 타입 (long_signal, short_signal)
  DateTime? lastSignalTime; // 마지막 신호 시간

  // Symbols
  final symbols = [
    'BTCUSDT',
    'ETHUSDT',
    'XRPUSDT',
    'SOLUSDT',
    'WLDUSDT',
    'DOGEUSDT'
  ];
  final leverages = [
    '5X',
    '10X',
    '15X',
    '20X',
    '25X',
    '30X',
    '50X',
    '75X',
    '100X'
  ];
  final investmentRatios = ['25%', '50%', '75%', '100%'];
  final hedgingThresholds = [
    '-0.5%',
    '-1.0%',
    '-1.5%',
    '-2.0%'
  ]; // 🟢 헷징 임계값 옵션

  // Status descriptions
  final statusDescriptions = {
    'IDLE': 'Bot is idle',
    'WAITING_LONG_SECONDARY': 'Waiting for long signal confirmation',
    'WAITING_SHORT_SECONDARY': 'Waiting for short signal confirmation',
    'LONG_ACTIVE': 'Long position active',
    'SHORT_ACTIVE': 'Short position active'
  };

  // ✅ 실제 데이터로 변경
  List<Map<String, dynamic>> activityData = [];

  late AnimationController _scrollController;
  Timer? _signalTimer;
  Timer? _uptimeTimer;
  Timer? _systemLogTimer; // 🟢 시스템 로그 업데이트 타이머

  @override
  void initState() {
    super.initState();
    _scrollController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    // ✅ Activity History 데이터 로드
    loadActivityHistory();

    // 🟢 앱 시작 시 바로 시스템 로그 로드 (로그인 여부와 관계없이)
    print('🔍 initState에서 시스템 로그 로드 시작');
    loadSystemLogs();
    startSystemLogTimer();

    // 🟢 앱 시작 시 바로 SignalR 연결 (봇 시작 여부와 관계없이 신호 수신)
    print('🔍 initState에서 SignalR 연결 시작');
    initializeSignalR();

    // 🟢 시장 가격 시뮬레이션 및 PnL 업데이트 타이머 시작
    startMarketSimulationTimer();
  }

  // ✅ Activity History 데이터 로드 함수
  Future<void> loadActivityHistory() async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/api/simple-rankings'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          activityData = List<Map<String, dynamic>>.from(data['rankings']);
        });
      }
    } catch (e) {
      print('Activity History 로드 오류: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _signalTimer?.cancel();
    _uptimeTimer?.cancel();
    accountStatsTimer?.cancel();
    _systemLogTimer?.cancel(); // 🟢 시스템 로그 타이머 정리
    super.dispose();
  }

  // Calculate bot uptime
  String getBotUptime() {
    if (botStartTime == null || !isBotRunning) return '00:00:00';

    final now = DateTime.now();
    final diff = now.difference(botStartTime!);

    final days = diff.inDays;
    final hours = diff.inHours % 24;
    final minutes = diff.inMinutes % 60;

    return '${days.toString().padLeft(2, '0')}:${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  // ✅ 추가: 간단한 로그인 검증 함수
  Future<bool> _authenticateUser(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/api/auth/simple-login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        currentUsername = data['username']; // ✅ 사용자명 저장
        return true;
      }
      return false;
    } catch (e) {
      print('로그인 오류: $e');
      return false;
    }
  }

  // Handle login - 백엔드 API 연결로 변경
  Future<void> handleLogin() async {
    final success =
        await _authenticateUser(loginForm['username']!, loginForm['password']!);

    if (success) {
      setState(() {
        isLoggedIn = true;
        isConnected = true;
      });
      // 🟢 SignalR은 이미 initState에서 연결되었으므로 중복 연결 방지
      // startSignalRConnection(); // 제거됨
    } else {
      _showAlert('Login failed: Invalid username or password.');
    }
  }

  // ✅ 추가: 회원가입 API 호출 함수
  Future<bool> _registerUser() async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/api/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(signUpForm),
      );

      return response.statusCode == 201;
    } catch (e) {
      print('회원가입 오류: $e');
      return false;
    }
  }

  // ✅ 추가: 프로필 수정 함수
  Future<void> updateProfile() async {
    if (currentUsername == null) {
      _showAlert('로그인이 필요합니다.');
      return;
    }

    // 유효성 검사
    if (profileForm['email']!.isNotEmpty &&
        !_isValidEmail(profileForm['email']!)) {
      _showAlert('올바른 이메일 형식을 입력해주세요.');
      return;
    }

    if (profileForm['new_password']!.isNotEmpty) {
      if (profileForm['current_password']!.isEmpty) {
        _showAlert('현재 비밀번호를 입력해주세요.');
        return;
      }
      if (profileForm['new_password']!.length < 6) {
        _showAlert('새 비밀번호는 6자 이상이어야 합니다.');
        return;
      }
      if (profileForm['new_password'] != profileForm['confirm_password']) {
        _showAlert('새 비밀번호가 일치하지 않습니다.');
        return;
      }
    }

    try {
      final requestBody = {
        'username': currentUsername,
        'email': profileForm['email'],
        'current_password': profileForm['current_password'],
        'new_password': profileForm['new_password'],
      };

      final response = await http.put(
        Uri.parse('$apiBaseUrl/api/auth/profile-simple'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _showAlert(data['message'] ?? '프로필이 업데이트되었습니다.');

        setState(() {
          showProfileEdit = false;
          profileForm.clear();
        });
      } else {
        final error = jsonDecode(response.body);
        _showAlert(error['error'] ?? '프로필 업데이트에 실패했습니다.');
      }
    } catch (e) {
      _showAlert('프로필 업데이트 중 오류가 발생했습니다.');
    }
  }

  // 이메일 유효성 검사
  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  // Handle sign up - 백엔드 API 연결로 변경
  Future<void> handleSignUp() async {
    if (signUpForm['username']!.isEmpty ||
        signUpForm['email']!.isEmpty ||
        signUpForm['password']!.isEmpty) {
      _showAlert('All fields are required.');
      return;
    }

    if (signUpForm['password'] != signUpForm['confirmPassword']) {
      _showAlert('Passwords do not match.');
      return;
    }

    if (signUpForm['password']!.length < 6) {
      _showAlert('Password must be at least 6 characters long.');
      return;
    }

    // ✅ 백엔드 회원가입 API 호출
    final success = await _registerUser();

    if (success) {
      _showAlert('Sign up successful, please log in.');
      setState(() {
        isSignUp = false;
        signUpForm.clear();
      });
    } else {
      _showAlert('Sign up failed. Please try again.');
    }
  }

  // 실시간 자산 정보 업데이트
  void updateAccountStats() async {
    if (!isConnected || currentUsername == null) return;

    try {
      final response = await http.post(
        // ✅ POST로 변경 (사용자명 전송)
        Uri.parse('$apiBaseUrl/api/bot/account/stats-with-user'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'username': currentUsername}), // ✅ 사용자명 전송
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          totalEquity = (data['total_equity'] ?? 0.0).toDouble();
          totalProfit = (data['total_profit'] ?? 0.0).toDouble();
          lastUpdateTime = data['last_update'] ?? '-';
        });
      }
    } catch (e) {
      print('자산 정보 업데이트 오류: $e');
    }
  }

  // 자산 정보 타이머 시작
  void startAccountStatsTimer() {
    accountStatsTimer?.cancel();
    accountStatsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      updateAccountStats();
    });
    // 즉시 한 번 업데이트
    updateAccountStats();
  }

  // 자산 정보 타이머 중지
  void stopAccountStatsTimer() {
    accountStatsTimer?.cancel();
    // 봇 중지 시에도 마지막 자산 정보 유지 (값 초기화 제거)
  }

  // Start/stop bot
  void handleBotToggle() async {
    if (!isConnected) {
      _showAlert('Please configure API settings first.');
      return;
    }

    try {
      if (isBotRunning) {
        // Stop Bot API 호출
        print('🛑 Stop Bot API 호출 시작...');
        final response = await http.post(
          Uri.parse('$apiBaseUrl/api/bot/stop'),
          headers: {'Content-Type': 'application/json'},
        );

        print('🛑 Stop Bot API 응답: ${response.statusCode}');
        print('🛑 Stop Bot API 메시지: ${response.body}');

        if (response.statusCode == 200) {
          print('🛑 Stop Bot 성공 - UI 상태 업데이트 중...');
          setState(() {
            isBotRunning = false;
            botStatus = 'IDLE';
            botStartTime = null;
            stopAccountStatsTimer();
          });
          print('🛑 UI 상태 업데이트 완료: isBotRunning = $isBotRunning');
          _showAlert('Bot stopped successfully.');
        } else {
          print('🛑 Stop Bot 실패: ${response.statusCode}');
          _showAlert('Failed to stop bot: ${response.body}', isSuccess: false);
        }
      } else {
        // Start Bot API 호출
        print('🚀 Start Bot API 호출 시작...');
        final response = await http.post(
          Uri.parse('$apiBaseUrl/api/bot/start'),
          headers: {'Content-Type': 'application/json'},
        );

        print('🚀 Start Bot API 응답: ${response.statusCode}');

        if (response.statusCode == 200) {
          print('🚀 Start Bot 성공 - UI 상태 업데이트 중...');
          setState(() {
            isBotRunning = true;
            botStatus = 'RUNNING'; // ← botStatus 업데이트 추가
            botStartTime = DateTime.now();
            startAccountStatsTimer();
            startUptimeTimer();
          });
          print(
              '🚀 UI 상태 업데이트 완료: isBotRunning = $isBotRunning, botStatus = $botStatus');
          _showAlert('Bot started successfully.');
        } else {
          print('🚀 Start Bot 실패: ${response.statusCode}');
          _showAlert('Failed to start bot: ${response.body}', isSuccess: false);
        }
      }
    } catch (e) {
      print('❌ handleBotToggle 오류: $e');
      _showAlert('Error: $e', isSuccess: false);
    }
  }

  // Start uptime timer
  void startUptimeTimer() {
    _uptimeTimer?.cancel();
    _uptimeTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && isBotRunning) {
        setState(() {});
      }
    });
  }

  // Save API keys
  void saveApiKeys() async {
    if (apiKeys['apiKey']!.isEmpty || apiKeys['apiSecret']!.isEmpty) {
      // API 키가 비어있으면 삭제 처리
      await _clearApiKeysInBackend();
      setState(() {
        isConnected = false;
        showApiModal = false;
      });
      _showAlert('API keys cleared successfully.');
      return;
    }

    // API 키가 있으면 저장 처리
    await _saveApiKeysToBackend();
    setState(() {
      isConnected = true;
      showApiModal = false;
    });
    _showAlert('API keys saved successfully.');
  }

  // Clear API keys in backend
  Future<void> _clearApiKeysInBackend() async {
    try {
      final response = await http.put(
        Uri.parse('$apiBaseUrl/api/mode/settings-no-auth'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'demo_api_key': '',
          'demo_api_secret': '',
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] != true) {
          _showAlert(
              'Failed to clear API keys: ${responseData['message'] ?? 'Unknown error'}');
        }
      } else {
        _showAlert('Failed to clear API keys: HTTP ${response.statusCode}');
      }
    } catch (e) {
      _showAlert('Error clearing API keys: $e');
    }
  }

  // Save API keys to backend
  Future<void> _saveApiKeysToBackend() async {
    try {
      // 디버깅: 전송할 데이터 확인
      print('🔑 API 키 전송 데이터:');
      print(
          '  apiKey: "${apiKeys['apiKey']}" (길이: ${apiKeys['apiKey']?.length ?? 0})');
      print(
          '  apiSecret: "${apiKeys['apiSecret']}" (길이: ${apiKeys['apiSecret']?.length ?? 0})');

      final requestBody = {
        'username': currentUsername, // ✅ 추가: 사용자명
        'demo_api_key': apiKeys['apiKey'],
        'demo_api_secret': apiKeys['apiSecret'],
      };

      print('🚀 전송할 JSON: ${jsonEncode(requestBody)}');

      final response = await http.put(
        Uri.parse('$apiBaseUrl/api/mode/settings-with-user'), // ✅ 새 엔드포인트
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] != true) {
          _showAlert(
              'Failed to save API keys: ${responseData['message'] ?? 'Unknown error'}');
        }
      } else {
        _showAlert('Failed to save API keys: HTTP ${response.statusCode}');
      }
    } catch (e) {
      _showAlert('Error saving API keys: $e');
    }
  }

  // Save settings to backend
  Future<void> saveSettingsToBackend() async {
    try {
      final response = await http.put(
        Uri.parse('$apiBaseUrl/api/mode/settings-no-auth'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'symbol': settings['symbol'],
          'leverage': settings['leverage'],
          'investment_ratio': settings['investmentRatio'],
          'hedging_threshold': settings['hedgingThreshold'] // 🟢 헷징 임계값 전송
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          _showAlert('Settings saved to backend successfully!');
        } else {
          _showAlert(
              'Failed to save settings: ${responseData['message'] ?? 'Unknown error'}');
        }
      } else {
        _showAlert('Failed to save settings: HTTP ${response.statusCode}');
      }
    } catch (e) {
      _showAlert('Error saving settings: $e');
    }
  }

  // Save settings
  void saveSettings() async {
    // 🟢 1단계: 백엔드 DB에 설정 저장
    await saveSettingsToBackend();

    // 🟢 2단계: 봇이 실행 중이면 동적 설정 업데이트 호출
    if (botStatus != 'IDLE') {
      await updateRunningBotSettings();
    }

    setState(() {
      showSettings = false;
    });
  }

  // 🟢 실행 중인 봇의 설정 동적 업데이트
  Future<void> updateRunningBotSettings() async {
    try {
      final response = await http.put(
        Uri.parse('$apiBaseUrl/api/bot/update-settings'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'symbol': settings['symbol'],
          'leverage': settings['leverage'],
          'investment_ratio': settings['investmentRatio'],
          'hedging_threshold': settings['hedgingThreshold']
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          _showAlert('✅ 실행 중인 봇 설정이 실시간으로 업데이트되었습니다!');

          // 업데이트된 필드 정보 표시
          final botResult = responseData['bot_result'];
          if (botResult != null && botResult['updated_fields'] != null) {
            final updatedFields =
                List<String>.from(botResult['updated_fields']);
            if (updatedFields.isNotEmpty) {
              _showAlert('업데이트된 설정: ${updatedFields.join(', ')}');
            }
          }
        } else {
          _showAlert(
              '봇 설정 업데이트 실패: ${responseData['error'] ?? 'Unknown error'}');
        }
      } else {
        _showAlert('봇 설정 업데이트 실패: HTTP ${response.statusCode}');
      }
    } catch (e) {
      _showAlert('봇 설정 업데이트 중 오류: $e');
    }
  }

  // 🟢 시스템 로그 파일에서 로그 로드
  Future<void> loadSystemLogs() async {
    try {
      print('🔍 시스템 로그 로드 시작...');
      final response = await http.get(
        Uri.parse('$apiBaseUrl/api/realtime-logs?limit=100'),
      );

      print('🔍 시스템 로그 API 응답: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print(
            '🔍 시스템 로그 데이터: ${data['success']}, 로그 개수: ${data['logs']?.length ?? 0}');

        if (data['success'] == true) {
          final systemLogs = List<Map<String, dynamic>>.from(data['logs']);
          print('🔍 파싱된 시스템 로그 개수: ${systemLogs.length}');

          // 시스템 로그 타입 확인
          for (var log in systemLogs.take(3)) {
            print('🔍 로그 샘플: type=${log['type']}, message=${log['message']}');
          }

          setState(() {
            int addedCount = 0;
            // 기존 로그와 시스템 로그 병합 (중복 제거)
            for (var systemLog in systemLogs) {
              // 동일한 시간과 메시지가 있는지 확인
              bool isDuplicate = logs.any((existingLog) =>
                  existingLog['time'] == systemLog['time'] &&
                  existingLog['message'] == systemLog['message']);

              if (!isDuplicate) {
                logs.add(systemLog);
                addedCount++;
              }
            }

            print('🔍 새로 추가된 로그 개수: $addedCount');

            // 시간순으로 정렬 (최신순)
            logs.sort((a, b) {
              final timeA = a['time'] as String;
              final timeB = b['time'] as String;
              return timeB.compareTo(timeA);
            });

            // 최대 500개 유지
            logs = logs.take(500).toList();

            print('🔍 총 로그 개수: ${logs.length}');

            // 시스템 로그 개수 확인
            final systemLogCount =
                logs.where((log) => log['type'] == 'system').length;
            print('🔍 시스템 타입 로그 개수: $systemLogCount');
          });
        }
      } else {
        print('🔍 시스템 로그 API 오류: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('시스템 로그 로드 오류: $e');
    }
  }

  // 🟢 시스템 로그 업데이트 타이머 시작
  void startSystemLogTimer() {
    _systemLogTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      loadSystemLogs();
    });
  }

  // 🟢 시장 가격 시뮬레이션 타이머 시작
  void startMarketSimulationTimer() {
    _signalTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      // 시장 가격 시뮬레이션
      setState(() {
        final priceChange = (math.Random().nextDouble() - 0.5) * 100;
        marketPrice += priceChange;
        lastSyncTime = DateTime.now();
      });

      if (currentPosition != null) {
        updatePnL();
      }
    });
  }

  // 🟢 더 이상 사용되지 않는 함수 (initState에서 직접 처리)
  // SignalR 실시간 연결 시작
  void startSignalRConnection() {
    // 이 함수는 더 이상 사용되지 않습니다.
    // 모든 초기화는 initState()에서 처리됩니다.
  }

  // 실제 SignalR 연결 초기화
  void initializeSignalR() async {
    try {
      hubConnection = HubConnectionBuilder()
          .withUrl('http://localhost:5000/signalr')
          .build();

      // 신호 수신 핸들러 (신호 + 거래 로그 통합)
      hubConnection!.on('SendSignal', (List<Object?>? arguments) {
        if (arguments != null && arguments.isNotEmpty) {
          setState(() {
            final String logMessage = arguments[0]?.toString() ?? '';
            if (logMessage.isNotEmpty) {
              // 메시지 타입 구분
              String logType;
              String displayMessage;

              if (logMessage.contains('[TRADE]') ||
                  logMessage.contains('ETH @') ||
                  logMessage.contains('포지션 동기화')) {
                // 거래 로그 (거래 및 포지션 관련 메시지)
                logType = 'trade';
                displayMessage =
                    logMessage.replaceFirst('[TRADE] ', ''); // 태그 제거
              } else if (logMessage.contains('[SYSTEM]') ||
                  logMessage.contains('[ERROR]') ||
                  logMessage.contains('봇이') ||
                  logMessage.contains('Stop Bot') ||
                  logMessage.contains('포지션 청산')) {
                // 시스템 로그 (봇 상태, 시스템 이벤트)
                logType = 'system';
                displayMessage = logMessage
                    .replaceFirst('[SYSTEM] ', '')
                    .replaceFirst('[ERROR] ', ''); // 태그 제거
              } else {
                // 일반 신호
                logType = 'signal';
                displayMessage = logMessage;
              }

              final newLog = {
                'id': DateTime.now().millisecondsSinceEpoch,
                'type': logType,
                'time': DateTime.now().toString().substring(11, 19),
                'message': displayMessage,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };

              // 새 로그를 맨 앞에 추가하고 최대 500개 유지 (더 많은 로그 보관)
              logs = [newLog, ...logs].take(500).toList();
            }
          });
        }
      });

      // 연결 상태 핸들러
      hubConnection!.onclose((error) {
        setState(() {
          isSignalRConnected = false;
          final errorLog = {
            'id': DateTime.now().millisecondsSinceEpoch,
            'type': 'error',
            'time': DateTime.now().toString().substring(11, 19),
            'message': 'SignalR 연결이 닫혔습니다: ${error ?? "알 수 없는 오류"}',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          };
          logs = [errorLog, ...logs].take(100).toList();
        });
      });

      // 연결 시작
      await hubConnection!.start();

      setState(() {
        isSignalRConnected = true;
        final successLog = {
          'id': DateTime.now().millisecondsSinceEpoch,
          'type': 'info',
          'time': DateTime.now().toString().substring(11, 19),
          'message': 'SignalR 연결 성공! 실시간 신호 수신 대기 중...',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        logs = [successLog, ...logs].take(100).toList();
      });
    } catch (e) {
      setState(() {
        isSignalRConnected = false;
        final errorLog = {
          'id': DateTime.now().millisecondsSinceEpoch,
          'type': 'error',
          'time': DateTime.now().toString().substring(11, 19),
          'message': 'SignalR 연결 실패: $e',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        logs = [errorLog, ...logs].take(100).toList();
      });
    }
  }

  // Process signal
  void processSignal(Map<String, dynamic> signal) {
    // 봇이 실행 중일 때만 신호 처리
    if (!isBotRunning || botStatus == 'IDLE' || botStatus == 'RUNNING') {
      return; // 봇 미실행 시 또는 대기 상태일 때 신호 무시
    }

    final colors = signal['colors'] as List<String>;

    setState(() {
      switch (botStatus) {
        case 'IDLE':
          if (colors.contains('green') && colors.contains('red')) {
            // Do nothing, conflicting signals
          } else if (colors.contains('green')) {
            botStatus = 'WAITING_LONG_SECONDARY';
            secondarySignalCount = 0;
          } else if (colors.contains('red')) {
            botStatus = 'WAITING_SHORT_SECONDARY';
            secondarySignalCount = 0;
          }
          break;

        case 'WAITING_LONG_SECONDARY':
          if (colors.contains('blue')) {
            secondarySignalCount++;
            if (secondarySignalCount >= 10) {
              enterPosition('LONG');
              secondarySignalCount = 0;
            }
          }
          break;

        case 'WAITING_SHORT_SECONDARY':
          if (colors.contains('yellow')) {
            secondarySignalCount++;
            if (secondarySignalCount >= 10) {
              enterPosition('SHORT');
              secondarySignalCount = 0;
            }
          }
          break;
      }
    });
  }

  // Enter position
  void enterPosition(String side) {
    setState(() {
      currentPosition = {
        'side': side,
        'entryPrice': marketPrice,
        'entryTime': TimeOfDay.now().format(context),
        'quantity': 0.1,
      };
      botStatus = side == 'LONG' ? 'LONG_ACTIVE' : 'SHORT_ACTIVE';
      secondarySignalCount = 0;
    });
  }

  // Update PnL
  void updatePnL() {
    if (currentPosition == null) return;

    double pnl;
    if (currentPosition!['side'] == 'LONG') {
      pnl = ((marketPrice - currentPosition!['entryPrice']) /
              currentPosition!['entryPrice']) *
          100;
    } else {
      pnl = ((currentPosition!['entryPrice'] - marketPrice) /
              currentPosition!['entryPrice']) *
          100;
    }

    setState(() {
      stats['currentPnL'] = pnl;
    });

    if (pnl <= -0.5) {
      closePosition('Stop loss triggered');
    }
  }

  // Close position
  void closePosition(String reason) {
    if (currentPosition == null) return;

    final finalPnL = stats['currentPnL'] as double;

    setState(() {
      stats['totalTrades'] = (stats['totalTrades'] as int) + 1;
      final totalTrades = stats['totalTrades'] as int;
      final currentWinRate = stats['winRate'] as double;

      if (finalPnL > 0) {
        stats['winRate'] =
            ((currentWinRate * (totalTrades - 1) + 1) / totalTrades);
      } else {
        stats['winRate'] = (currentWinRate * (totalTrades - 1) / totalTrades);
      }

      stats['totalProfit'] = (stats['totalProfit'] as double) + finalPnL;
      stats['currentPnL'] = 0.0;

      currentPosition = null;
      botStatus = 'IDLE';
      secondarySignalCount = 0;
    });
  }

  // Handle logout
  void handleLogout() async {
    // SignalR 연결 정리
    if (hubConnection != null) {
      try {
        await hubConnection!.stop();
        hubConnection = null;
      } catch (e) {
        // 연결 정리 중 오류 무시
      }
    }

    setState(() {
      isLoggedIn = false;
      isConnected = false;
      isBotRunning = false;
      isSignalRConnected = false;

      // 신호 상태 초기화
      lastSignalType = null;
      lastSignalTime = null;
      logs.clear(); // 로그도 초기화

      // 로그아웃 시 자산 정보 초기화
      totalEquity = 0.0;
      totalProfit = 0.0;
      lastUpdateTime = '-';
    });
    _signalTimer?.cancel();
    _uptimeTimer?.cancel();
  }

  // Show alert message
  void _showAlert(String message, {bool isSuccess = true}) {
    if (!mounted) return;

    setState(() {
      statusMessage = message;
      showStatusCard = true;
      isStatusSuccess = isSuccess;
    });

    // 3초 후 자동 사라짐
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          showStatusCard = false;
        });
      }
    });
  }

  // Build status card
  Widget _buildStatusCard() {
    if (!showStatusCard || statusMessage == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 120, // opensystems 카드(50px) + 갭(16px) + 여유(54px)
      left: 16,
      right: 16,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isStatusSuccess
              ? Colors.green.withValues(alpha: 0.9)
              : Colors.red.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              isStatusSuccess ? Icons.check_circle : Icons.error,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                statusMessage!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!isLoggedIn) {
      return _buildLoginScreen();
    }

    return _buildMainDashboard();
  }

  // Login screen
  Widget _buildLoginScreen() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final maxWidth =
                    constraints.maxWidth > 400 ? 400.0 : constraints.maxWidth;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo section
                    Container(
                      margin: const EdgeInsets.only(bottom: 48),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Glow effect
                          Container(
                            width: 200,
                            height: 200,
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.6),
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.3),
                                  blurRadius: 40,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                          ),
                          // Main card
                          Container(
                            padding: const EdgeInsets.all(32),
                            constraints: BoxConstraints(maxWidth: maxWidth),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2A2A2A),
                              borderRadius: BorderRadius.circular(24),
                              border:
                                  Border.all(color: const Color(0xFF8B4513)),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black54,
                                  blurRadius: 20,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Bitcoin icon
                                Container(
                                  width: 64,
                                  height: 64,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF2C1810),
                                        Color(0xFF1A0F08)
                                      ],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                        color: const Color(0xFF8B4513)),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      '🔔',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFFF7931A),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'Trading Platform',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Bybit Automated Trading System',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Login form
                    Container(
                      width: double.infinity,
                      constraints: BoxConstraints(maxWidth: maxWidth),
                      child: Column(
                        children: [
                          Text(
                            isSignUp ? 'Sign Up' : 'Login',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 32),
                          if (isSignUp) ...[
                            // Sign up fields
                            _buildTextField(
                              icon: Icons.person,
                              hint: 'Username',
                              value: signUpForm['username'] ?? '',
                              onChanged: (value) => setState(
                                  () => signUpForm['username'] = value),
                            ),
                            const SizedBox(height: 16),
                            _buildTextField(
                              icon: Icons.email,
                              hint: 'Email',
                              value: signUpForm['email'] ?? '',
                              onChanged: (value) =>
                                  setState(() => signUpForm['email'] = value),
                            ),
                            const SizedBox(height: 16),
                            _buildTextField(
                              icon: Icons.lock,
                              hint: 'Password',
                              value: signUpForm['password'] ?? '',
                              onChanged: (value) => setState(
                                  () => signUpForm['password'] = value),
                              isPassword: true,
                              showPassword: showPassword,
                              onTogglePassword: () =>
                                  setState(() => showPassword = !showPassword),
                            ),
                            const SizedBox(height: 16),
                            _buildTextField(
                              icon: Icons.lock,
                              hint: 'Confirm Password',
                              value: signUpForm['confirmPassword'] ?? '',
                              onChanged: (value) => setState(
                                  () => signUpForm['confirmPassword'] = value),
                              isPassword: true,
                            ),
                            const SizedBox(height: 24),
                            _buildButton(
                              text: 'Sign Up',
                              onPressed: handleSignUp,
                            ),
                          ] else ...[
                            // Login fields
                            _buildTextField(
                              icon: Icons.email,
                              hint: 'Email',
                              value: loginForm['username'] ?? '',
                              onChanged: (value) =>
                                  setState(() => loginForm['username'] = value),
                            ),
                            const SizedBox(height: 16),
                            _buildTextField(
                              icon: Icons.lock,
                              hint: 'Password',
                              value: loginForm['password'] ?? '',
                              onChanged: (value) =>
                                  setState(() => loginForm['password'] = value),
                              isPassword: true,
                              showPassword: showPassword,
                              onTogglePassword: () =>
                                  setState(() => showPassword = !showPassword),
                            ),
                            const SizedBox(height: 24),
                            _buildButton(
                              text: 'Login',
                              onPressed: handleLogin,
                            ),
                          ],
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                isSignUp
                                    ? 'Already have an account? '
                                    : 'Don\'t have an account? ',
                                style: const TextStyle(color: Colors.grey),
                              ),
                              GestureDetector(
                                onTap: () =>
                                    setState(() => isSignUp = !isSignUp),
                                child: Text(
                                  isSignUp ? 'Login' : 'Sign Up',
                                  style: const TextStyle(
                                    color: Colors.orange,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Open Systems badge
                    const SizedBox(height: 48),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border: Border.all(color: const Color(0xFF8B4513)),
                      ),
                      child: const Text(
                        'OPEN SYSTEMS',
                        style: TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Main dashboard
  Widget _buildMainDashboard() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isWide = constraints.maxWidth > 900;
          return Stack(
            children: [
              // Main content
              Column(
                children: [
                  // Top bar
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF1A1A1A).withValues(alpha: 0.8),
                      border: const Border(
                        bottom: BorderSide(color: Color(0xFF333333)),
                      ),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF2C1810),
                                        Color(0xFF1A0F08)
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        color: const Color(0xFF8B4513)),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0xFF8B4513)
                                            .withValues(alpha: 0.3),
                                        blurRadius: 20,
                                      ),
                                    ],
                                  ),
                                  child: const Center(
                                    child: Text(
                                      '🔔',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFFF7931A),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ShaderMask(
                                      shaderCallback: (bounds) =>
                                          const LinearGradient(
                                        colors: [Colors.orange, Colors.yellow],
                                      ).createShader(bounds),
                                      child: const Text(
                                        'Trading Platform',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    const Text(
                                      'Automated Trading System v34',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            IconButton(
                              icon:
                                  const Icon(Icons.logout, color: Colors.grey),
                              onPressed: handleLogout,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Content area
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.only(bottom: 130),
                      scrollDirection: Axis.vertical,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: isWide
                            ? Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Main content (tabs)
                                  Expanded(child: _buildTabContent()),
                                  const SizedBox(width: 24),
                                  // Ranking or extra panel (optional)
                                  // You can add a side panel here if needed
                                ],
                              )
                            : _buildTabContent(),
                      ),
                    ),
                  ),
                ],
              ),

              // OpenSystems fixed card
              Positioned(
                bottom: 64,
                left: 0,
                right: 0,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  height: 50,
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: const Color(0xFF333333)),
                  ),
                  child: Center(
                    child: ShaderMask(
                      shaderCallback: (bounds) => const LinearGradient(
                        colors: [Colors.orange, Colors.yellow],
                      ).createShader(bounds),
                      child: const Text(
                        'opensystems',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Status card (opensystems 카드 위에 위치)
              _buildStatusCard(),

              // Bottom navigation menu
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFF333333)),
                    ),
                  ),
                  child: BottomNavigationBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    currentIndex: _getTabIndex(),
                    onTap: _onTabTapped,
                    type: BottomNavigationBarType.fixed,
                    selectedItemColor: Colors.orange,
                    unselectedItemColor: Colors.grey,
                    items: const [
                      BottomNavigationBarItem(
                        icon: Icon(Icons.home),
                        label: 'Home',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.key),
                        label: 'API KEY',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.settings),
                        label: 'Settings',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.account_circle),
                        label: 'Profile',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Get current tab index
  int _getTabIndex() {
    switch (activeTab) {
      case 'home':
        return 0;
      case 'login':
        return 1;
      case 'settings':
        return 2;
      case 'profile':
        return 3;
      default:
        return 0;
    }
  }

  // Handle tab selection
  void _onTabTapped(int index) {
    setState(() {
      switch (index) {
        case 0:
          activeTab = 'home';
          break;
        case 1:
          activeTab = 'login';
          break;
        case 2:
          activeTab = 'settings';
          break;
        case 3:
          activeTab = 'profile';
          break;
      }
    });
  }

  // Build tab content
  Widget _buildTabContent() {
    switch (activeTab) {
      case 'home':
        return _buildHomeTab();
      case 'login':
        return _buildApiKeyTab();
      case 'settings':
        return _buildSettingsTab();
      case 'profile':
        return _buildProfileTab();
      default:
        return _buildHomeTab();
    }
  }

  // Home tab
  Widget _buildHomeTab() {
    return Column(
      children: [
        // Bot status card
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: botStatus == 'IDLE' ? Colors.red : Colors.green,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Bot Status',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    getBotUptime(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isBotRunning
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.red.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: isBotRunning ? Colors.green : Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (botStatus != 'IDLE') ...[
                const SizedBox(height: 12),
                Text(
                  statusDescriptions[botStatus] ?? '',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ],
              if (secondarySignalCount > 0) ...[
                const SizedBox(height: 8),
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Secondary Signal Progress',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          '$secondarySignalCount/10',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.yellow,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: secondarySignalCount / 10,
                      backgroundColor: Colors.grey.shade800,
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.orange),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        // Profit stats
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF333333)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.orange.withValues(alpha: 0.2),
                              Colors.yellow.withValues(alpha: 0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.attach_money,
                          color: Color(0xFFF7931A),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Profit Stats',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: isConnected
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.red.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isConnected
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      isConnected ? 'API Connected' : 'API Disconnected',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isConnected ? Colors.green : Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.grey.shade800.withValues(alpha: 0.5),
                            Colors.grey.shade900.withValues(alpha: 0.5),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.shade700.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.attach_money,
                            color: Colors.orange.shade400,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Total Equity',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '\$${totalEquity.toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.grey.shade800.withValues(alpha: 0.5),
                            Colors.grey.shade900.withValues(alpha: 0.5),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.shade700.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.trending_up,
                            color: Colors.green.shade400,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Total Profit (총 수익)',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${totalProfit >= 0 ? '+' : ''}${totalProfit.toStringAsFixed(2)}%',
                            style: TextStyle(
                              color:
                                  totalProfit >= 0 ? Colors.green : Colors.red,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.access_time, size: 12, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'Last update: $lastUpdateTime',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Start/stop buttons
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF333333)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: (!isConnected || isBotRunning)
                          ? null
                          : handleBotToggle,
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Start Bot'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.green,
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: (!isConnected || isBotRunning)
                                ? Colors.grey.shade700
                                : Colors.green.withValues(alpha: 0.5),
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: (!isConnected || !isBotRunning)
                          ? null
                          : handleBotToggle,
                      icon: const Icon(Icons.stop),
                      label: const Text('Stop Bot'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.red,
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: (!isConnected || !isBotRunning)
                                ? Colors.grey.shade700
                                : Colors.red.withValues(alpha: 0.5),
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (!isConnected) ...[
                const SizedBox(height: 12),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.warning, size: 12, color: Colors.yellow),
                    SizedBox(width: 4),
                    Text(
                      'API settings required',
                      style: TextStyle(
                        color: Colors.yellow,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        // Logs output
        Container(
          height: 500,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF333333)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.orange.withValues(alpha: 0.2),
                              Colors.yellow.withValues(alpha: 0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.article,
                          color: Color(0xFFF7931A),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Logs Output',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  // Log filter buttons
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade800.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        _buildLogFilterButton(
                            'signal', '🔔', const Color(0xFF4ADE80)),
                        _buildLogFilterButton(
                            'system', '⚙️', const Color(0xFF60A5FA)),
                        _buildLogFilterButton(
                            'trade', '💰', const Color(0xFFF59E0B)),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Logs list
              Expanded(
                child: ListView.builder(
                  itemCount: logs
                      .where((log) =>
                          logFilter == 'all' || log['type'] == logFilter)
                      .length,
                  itemBuilder: (context, index) {
                    final filteredLogs = logs
                        .where((log) =>
                            logFilter == 'all' || log['type'] == logFilter)
                        .toList();
                    final log = filteredLogs[index];

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1A1A1A).withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.grey.shade700.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            log['time'],
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                              fontFamily: 'monospace',
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              log['message'],
                              style: TextStyle(
                                fontSize: 14,
                                color: log['type'] == 'signal'
                                    ? Colors.green
                                    : log['type'] == 'system'
                                        ? Colors.blue
                                        : Colors.yellow,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // API Key tab
  Widget _buildApiKeyTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Column(
        children: [
          const Text(
            'API Key Settings',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),

          // API Key input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'API Key',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                initialValue: apiKeys['apiKey'],
                onChanged: (value) => setState(() => apiKeys['apiKey'] = value),
                obscureText: !showApiKey,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter your API Key',
                  hintStyle: const TextStyle(color: Colors.grey),
                  filled: true,
                  fillColor: const Color(0xFF1A1A1A),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF666666)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF666666)),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showApiKey ? Icons.visibility_off : Icons.visibility,
                      color: Colors.grey,
                    ),
                    onPressed: () => setState(() => showApiKey = !showApiKey),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // API Secret input
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'API Secret',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                initialValue: apiKeys['apiSecret'],
                onChanged: (value) =>
                    setState(() => apiKeys['apiSecret'] = value),
                obscureText: !showApiSecret,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter your API Secret',
                  hintStyle: const TextStyle(color: Colors.grey),
                  filled: true,
                  fillColor: const Color(0xFF1A1A1A),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF666666)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF666666)),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showApiSecret ? Icons.visibility_off : Icons.visibility,
                      color: Colors.grey,
                    ),
                    onPressed: () =>
                        setState(() => showApiSecret = !showApiSecret),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Security notice
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.yellow.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.yellow.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.yellow.shade500,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Security Notice',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.yellow.shade500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'API keys are sensitive and should be kept secret.\nDo not share your API keys with anyone.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Save button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: saveApiKeys,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: const BorderSide(
                    color: Color(0xFFF7931A),
                    width: 2,
                  ),
                ),
              ),
              child: const Text(
                'Save API Keys',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Settings tab
  Widget _buildSettingsTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF333333)),
      ),
      child: Column(
        children: [
          const Text(
            'Trading Settings',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),

          // Trading pair
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Trading Pair',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF666666)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: settings['symbol'],
                    isExpanded: true,
                    dropdownColor: const Color(0xFF2A2A2A),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => settings['symbol'] = value);
                      }
                    },
                    items: symbols.map((symbol) {
                      return DropdownMenuItem(
                        value: symbol,
                        child: Text(symbol),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Leverage
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Leverage',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF666666)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: settings['leverage'],
                    isExpanded: true,
                    dropdownColor: const Color(0xFF2A2A2A),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => settings['leverage'] = value);
                      }
                    },
                    items: leverages.map((leverage) {
                      return DropdownMenuItem(
                        value: leverage,
                        child: Text(leverage),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Investment ratio
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Investment Ratio',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF666666)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: settings['investmentRatio'],
                    isExpanded: true,
                    dropdownColor: const Color(0xFF2A2A2A),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => settings['investmentRatio'] = value);
                      }
                    },
                    items: investmentRatios.map((ratio) {
                      return DropdownMenuItem(
                        value: ratio,
                        child: Text(ratio),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 🟢 Hedging Threshold (헷징 임계값)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Hedging Threshold',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF666666)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: settings['hedgingThreshold'],
                    isExpanded: true,
                    dropdownColor: const Color(0xFF2A2A2A),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => settings['hedgingThreshold'] = value);
                      }
                    },
                    items: hedgingThresholds.map((threshold) {
                      return DropdownMenuItem(
                        value: threshold,
                        child: Text(threshold),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Save button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: saveSettings,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: const BorderSide(
                    color: Color(0xFFF7931A),
                    width: 2,
                  ),
                ),
              ),
              child: const Text(
                'Save Settings',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Profile tab
  Widget _buildProfileTab() {
    return Column(
      children: [
        // User info card
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF333333)),
          ),
          child: Row(
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withValues(alpha: 0.2),
                      Colors.yellow.withValues(alpha: 0.2),
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.account_circle,
                  color: Color(0xFFF7931A),
                  size: 40,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      loginForm['username'] ?? '<EMAIL>',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Row(
                      children: [
                        Text(
                          'Rank: ',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          '28th',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 16),
                        Text(
                          'Win Rate: ',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          '+45.2%',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Activity history
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A).withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF333333)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.orange.withValues(alpha: 0.2),
                          Colors.yellow.withValues(alpha: 0.2),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.bar_chart,
                      color: Color(0xFFF7931A),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Activity History',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  const Text(
                    'Last updated:',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Activity log (dummy data)
              Container(
                height: 400,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.shade700.withValues(alpha: 0.5),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AnimatedBuilder(
                    animation: _scrollController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, -_scrollController.value * 1000),
                        child: ListView(
                          physics: const NeverScrollableScrollPhysics(),
                          children: [
                            // Duplicate activity log for infinite scroll effect
                            ...List.generate(2, (setIndex) {
                              return Column(
                                children: activityData.map((user) {
                                  return Container(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF1A1A1A)
                                          .withValues(alpha: 0.4),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Colors.transparent,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 32,
                                              height: 32,
                                              decoration: BoxDecoration(
                                                gradient: user['rank'] <= 3
                                                    ? LinearGradient(
                                                        colors: [
                                                          Colors.orange
                                                              .withValues(
                                                                  alpha: 0.3),
                                                          Colors.yellow
                                                              .withValues(
                                                                  alpha: 0.3),
                                                        ],
                                                      )
                                                    : null,
                                                color: user['rank'] > 3
                                                    ? Colors.grey.shade800
                                                        .withValues(alpha: 0.5)
                                                    : null,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  user['medal'] ??
                                                      user['rank'].toString(),
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  user['username'] ??
                                                      'Unknown', // ✅ 실제 사용자명
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                Text(
                                                  '${user['total_trades']} trades', // ✅ 실제 거래 수
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            Text(
                                              '${user['profit_percentage'] >= 0 ? '+' : ''}${user['profit_percentage']}%', // ✅ 실제 수익률
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    user['profit_percentage'] >=
                                                            0
                                                        ? Colors.green
                                                        : Colors.red,
                                              ),
                                            ),
                                            const Text(
                                              'ROI',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList(),
                              );
                            }),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.only(top: 16),
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Color(0xFF444444)),
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Your Rank',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          '28th',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          '(+45.2%)',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Log filter buttons
  Widget _buildLogFilterButton(String value, String icon, Color color) {
    final isSelected = logFilter == value;

    return GestureDetector(
      onTap: () => setState(() => logFilter = value),
      child: Container(
        width: 32,
        height: 32,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.3) : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
          ),
        ),
        child: Center(
          child: Text(
            icon,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }

  // Text field widget
  Widget _buildTextField({
    required IconData icon,
    required String hint,
    required String value,
    required Function(String) onChanged,
    bool isPassword = false,
    bool? showPassword,
    VoidCallback? onTogglePassword,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF3A3A3A),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        obscureText: isPassword && !(showPassword ?? false),
        style: const TextStyle(color: Colors.white),
        onChanged: onChanged,
        decoration: InputDecoration(
          prefixIcon: Icon(icon, color: Colors.grey),
          hintText: hint,
          hintStyle: const TextStyle(color: Colors.grey),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          suffixIcon: isPassword && onTogglePassword != null
              ? IconButton(
                  icon: Icon(
                    showPassword ?? false
                        ? Icons.visibility_off
                        : Icons.visibility,
                    color: Colors.grey,
                  ),
                  onPressed: onTogglePassword,
                )
              : null,
        ),
      ),
    );
  }

  // Button widget
  Widget _buildButton({
    required String text,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFF7931A),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
