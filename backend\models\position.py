# 포지션 모델
# models/position.py
import datetime
import json
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from database import Base
from utils.helpers import utc_now

class Position(Base):
    """포지션 모델"""
    __tablename__ = 'positions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    symbol = Column(String(20), nullable=False)
    position_type = Column(String(20), nullable=False)  # LONG, SHORT, BIDIRECTIONAL
    entry_price = Column(Float, nullable=False)
    size = Column(Float, nullable=False)
    entry_time = Column(DateTime, default=datetime.datetime.utcnow)
    exit_time = Column(DateTime, nullable=True)
    exit_price = Column(Float, nullable=True)
    current_pnl = Column(Float, default=0.0)
    status = Column(String(20), default="ACTIVE")  # ACTIVE, CLOSED
    meta_data = Column(Text, nullable=True)  # 추가 메타데이터 (JSON)
    
    # 관계 설정
    user = relationship("User", back_populates="positions")
    trades = relationship("Trade", back_populates="position")
    
    def __init__(self, user_id, symbol, position_type, entry_price, size, metadata=None):
        """
        포지션 초기화
        
        Args:
            user_id: 사용자 ID
            symbol: 거래 심볼
            position_type: 포지션 타입 (LONG, SHORT, BIDIRECTIONAL)
            entry_price: 진입 가격
            size: 포지션 크기
            metadata: 추가 메타데이터
        """
        self.user_id = user_id
        self.symbol = symbol
        self.position_type = position_type
        self.entry_price = entry_price
        self.size = size
        self.entry_time = utc_now()
        self.status = "ACTIVE"
        
        if metadata:
            self.set_metadata(metadata)
    
    def set_metadata(self, metadata):
        """메타데이터 설정"""
        if isinstance(metadata, dict):
            self.meta_data = json.dumps(metadata)
        else:
            self.meta_data = metadata

    def get_metadata(self):
        """메타데이터 조회"""
        if not self.meta_data:
            return {}

        try:
            return json.loads(self.meta_data)
        except json.JSONDecodeError:
            return {}
    
    def close(self, exit_price):
        """포지션 종료"""
        self.exit_price = exit_price
        self.exit_time = utc_now()
        self.status = "CLOSED"
        
        # 최종 손익 계산
        if self.position_type == "LONG":
            self.current_pnl = ((exit_price - self.entry_price) / self.entry_price) * 100
        elif self.position_type == "SHORT":
            self.current_pnl = ((self.entry_price - exit_price) / self.entry_price) * 100
        else:  # BIDIRECTIONAL
            # 메타데이터에서 롱/숏 정보 가져오기
            metadata = self.get_metadata()
            long_entry = metadata.get('long_entry_price', self.entry_price)
            short_entry = metadata.get('short_entry_price', self.entry_price)
            long_pnl = ((exit_price - long_entry) / long_entry) * 100
            short_pnl = ((short_entry - exit_price) / short_entry) * 100
            # 평균 손익 계산
            self.current_pnl = (long_pnl + short_pnl) / 2
    
    def update_pnl(self, current_price):
        """현재 손익 업데이트"""
        if self.position_type == "LONG":
            self.current_pnl = ((current_price - self.entry_price) / self.entry_price) * 100
        elif self.position_type == "SHORT":
            self.current_pnl = ((self.entry_price - current_price) / self.entry_price) * 100
        else:  # BIDIRECTIONAL
            # 메타데이터에서 롱/숏 정보 가져오기
            metadata = self.get_metadata()
            long_entry = metadata.get('long_entry_price', self.entry_price)
            short_entry = metadata.get('short_entry_price', self.entry_price)
            long_pnl = ((current_price - long_entry) / long_entry) * 100
            short_pnl = ((short_entry - current_price) / short_entry) * 100
            # 평균 손익 계산
            self.current_pnl = (long_pnl + short_pnl) / 2
    
    def to_dict(self):
        """
        포지션 정보를 딕셔너리로 변환
        
        Returns:
            포지션 정보 딕셔너리
        """
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'symbol': self.symbol,
            'position_type': self.position_type,
            'entry_price': self.entry_price,
            'size': self.size,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'current_pnl': self.current_pnl,
            'status': self.status
        }
        
        if self.status == "CLOSED":
            result.update({
                'exit_price': self.exit_price,
                'exit_time': self.exit_time.isoformat() if self.exit_time else None
            })
        
        # 메타데이터 추가
        metadata = self.get_metadata()
        if metadata:
            result.update({'metadata': metadata})
        
        return result
    
    def __repr__(self):
        return f"<Position(id={self.id}, user_id={self.user_id}, symbol='{self.symbol}', type='{self.position_type}', status='{self.status}')>"