# 수정된 Bybit V5 클라이언트 (주요 문제점 해결)
# api_client/bybit_v5_client_fixed.py

import logging
from pybit.unified_trading import HTTP
import time
from datetime import datetime

logger = logging.getLogger("opensystems_bot")

class BybitV5ClientFixed:
    """
    수정된 Bybit API v5 클라이언트
    - 무한 재귀 문제 해결
    - 중복 메서드 제거  
    - 오류 처리 개선
    """
    
    def __init__(self, api_key, api_secret, is_demo=True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.is_demo = is_demo
        
        # 데모 모드에 따른 URL 설정
        if is_demo:
            # 실제로는 Demo Trading 키가 실제 서버를 사용하므로
            # 테스트넷이 아닌 실제 서버 사용
            self.base_url = "https://api.bybit.com"
            testnet = False
        else:
            self.base_url = "https://api.bybit.com"
            testnet = False
        
        self.client = HTTP(
            testnet=testnet,
            api_key=api_key,
            api_secret=api_secret
        )
        
        logger.info(f"BybitV5Client initialized: mode={'Demo' if is_demo else 'Real'}")
    
    def test_connection(self):
        """API 연결 테스트"""
        try:
            # 공개 API로 연결 테스트 (인증 불필요)
            result = self.client.get_server_time()
            if result.get('retCode') == 0:
                logger.info("API connection test successful")
                return True
            else:
                logger.error(f"API connection test failed: {result.get('retMsg')}")
                return False
        except Exception as e:
            logger.error(f"API connection test error: {e}")
            return False
    
    def get_server_time(self):
        """서버 시간 조회 - 중복 제거"""
        try:
            result = self.client.get_server_time()
            if result.get('retCode') == 0:
                server_time = result.get('result', {}).get('timeSecond')
                return {
                    'server_time': server_time,
                    'server_time_ms': result.get('result', {}).get('timeNano'),
                    'local_time': int(time.time())
                }
            else:
                logger.error(f"Failed to get server time: {result.get('retMsg')}")
                return None
        except Exception as e:
            logger.error(f"Error getting server time: {e}")
            return None
    
    def get_ticker_data(self, symbol):
        """티커 데이터 조회 - 무한 재귀 방지"""
        try:
            result = self.client.get_tickers(category="linear", symbol=symbol)
            if result.get('retCode') == 0:
                tickers = result.get('result', {}).get('list', [])
                if tickers:
                    return tickers[0]
                return None
            else:
                logger.error(f"Failed to get ticker: {result.get('retMsg')}")
                return None
        except Exception as e:
            logger.error(f"Error getting ticker: {e}")
            return None
    
    def get_current_price(self, symbol):
        """현재가 조회 - 직접 구현으로 재귀 방지"""
        ticker = self.get_ticker_data(symbol)  # get_ticker 대신 get_ticker_data 사용
        if ticker and 'lastPrice' in ticker:
            return float(ticker['lastPrice'])
        return None
    
    def get_wallet_balance(self):
        """지갑 잔고 조회 - 오류 정보 개선"""
        try:
            result = self.client.get_wallet_balance(accountType="UNIFIED")
            if result.get('retCode') == 0:
                balances = result.get('result', {}).get('list', [])
                if balances:
                    return {
                        'success': True,
                        'data': balances[0]
                    }
                return {
                    'success': True,
                    'data': {},
                    'message': 'No balance data found'
                }
            else:
                return {
                    'success': False,
                    'error_code': result.get('retCode'),
                    'error_msg': result.get('retMsg'),
                    'message': 'Failed to get wallet balance'
                }
        except Exception as e:
            logger.error(f"Error getting wallet balance: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Exception occurred while getting wallet balance'
            }
    
    def get_positions(self, symbol=None):
        """포지션 조회 - 오류 정보 개선"""
        try:
            params = {"category": "linear"}
            if symbol:
                params["symbol"] = symbol
            
            result = self.client.get_positions(**params)
            
            if result.get('retCode') == 0:
                positions = result.get('result', {}).get('list', [])
                return {
                    'success': True,
                    'data': positions,
                    'count': len(positions)
                }
            else:
                return {
                    'success': False,
                    'error_code': result.get('retCode'),
                    'error_msg': result.get('retMsg'),
                    'message': 'Failed to get positions'
                }
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Exception occurred while getting positions'
            }

    # 기타 메서드들도 동일한 패턴으로 개선...
