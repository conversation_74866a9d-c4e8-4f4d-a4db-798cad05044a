#!/usr/bin/env python3
# 기존 opensystems_bot.db에 profit_tracking 테이블 추가
# add_profit_tracking_table.py

import sqlite3
import os
from datetime import datetime

def add_profit_tracking_table():
    """기존 opensystems_bot.db에 profit_tracking 테이블 추가"""
    
    # 데이터베이스 파일 경로
    db_path = 'backend/db/opensystems_bot.db'
    
    # 데이터베이스 파일 존재 확인
    if not os.path.exists(db_path):
        print(f'❌ 데이터베이스 파일을 찾을 수 없습니다: {db_path}')
        return False
    
    try:
        # SQLite 연결
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f'📊 데이터베이스 연결: {db_path}')
        
        # profit_tracking 테이블이 이미 존재하는지 확인
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='profit_tracking'
        """)
        
        if cursor.fetchone():
            print('⚠️  profit_tracking 테이블이 이미 존재합니다.')
            conn.close()
            return True
        
        # profit_tracking 테이블 생성
        cursor.execute('''
        CREATE TABLE profit_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            initial_balance DECIMAL(15,8) NOT NULL,
            initial_equity DECIMAL(15,8) NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME,
            is_active BOOLEAN DEFAULT 1,
            reset_reason VARCHAR(100),
            session_count INTEGER DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')
        
        print('✅ profit_tracking 테이블 생성 완료')
        
        # 인덱스 생성 (성능 최적화)
        cursor.execute('''
        CREATE INDEX idx_profit_tracking_user_id ON profit_tracking(user_id)
        ''')
        
        cursor.execute('''
        CREATE INDEX idx_profit_tracking_active ON profit_tracking(is_active)
        ''')
        
        print('✅ 인덱스 생성 완료')
        
        # 변경사항 저장
        conn.commit()
        
        # 테이블 생성 확인
        cursor.execute("""
            SELECT sql FROM sqlite_master 
            WHERE type='table' AND name='profit_tracking'
        """)
        
        table_schema = cursor.fetchone()
        if table_schema:
            print('✅ 테이블 생성 검증 완료')
            print(f'📋 테이블 스키마:\n{table_schema[0]}')
        
        # 연결 종료
        conn.close()
        
        print('🎉 profit_tracking 테이블 추가 성공!')
        return True
        
    except Exception as e:
        print(f'❌ 오류 발생: {e}')
        if 'conn' in locals():
            conn.close()
        return False

def verify_table_structure():
    """테이블 구조 확인"""
    db_path = 'backend/db/opensystems_bot.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 테이블 정보 조회
        cursor.execute("PRAGMA table_info(profit_tracking)")
        columns = cursor.fetchall()
        
        print('\n📋 profit_tracking 테이블 구조:')
        print('=' * 50)
        for col in columns:
            print(f'{col[1]:20} {col[2]:15} {"NOT NULL" if col[3] else "NULL":8} {"PK" if col[5] else ""}')
        
        # 외래키 확인
        cursor.execute("PRAGMA foreign_key_list(profit_tracking)")
        foreign_keys = cursor.fetchall()
        
        if foreign_keys:
            print('\n🔗 외래키:')
            for fk in foreign_keys:
                print(f'  {fk[3]} -> {fk[2]}.{fk[4]}')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 테이블 구조 확인 오류: {e}')

if __name__ == '__main__':
    print('🚀 profit_tracking 테이블 추가 시작...\n')
    
    success = add_profit_tracking_table()
    
    if success:
        verify_table_structure()
        print('\n✅ 모든 작업이 완료되었습니다!')
    else:
        print('\n❌ 작업이 실패했습니다.')
