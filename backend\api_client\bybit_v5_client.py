# Bybit API v5 클라이언트
# api_client/bybit_v5_client.py
import logging
from pybit.unified_trading import HTTP
import time
from datetime import datetime

logger = logging.getLogger("opensystems_bot")

class BybitV5Client:
    """
    Bybit API v5 클라이언트
    동일한 인터페이스로 데모 모드와 리얼 모드 모두 지원
    """
    
    def __init__(self, api_key, api_secret, is_demo=True):
        """
        Bybit API v5 클라이언트 초기화

        Args:
            api_key: Bybit API 키
            api_secret: Bybit API 시크릿
            is_demo: 데모 모드 여부 (True: 데모, False: 리얼)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.is_demo = is_demo

        # API URL 설정 (데모/리얼 모드에 따라 구분)
        if is_demo:
            self.base_url = "https://api-demo.bybit.com"
        else:
            self.base_url = "https://api.bybit.com"

        # 클라이언트 초기화
        # 데모 모드에서는 testnet=False, 리얼 모드에서도 testnet=False
        # pybit 라이브러리는 testnet=True일 때 api-testnet.bybit.com을 사용
        # 데모 모드(api-demo.bybit.com)는 별도 처리 필요
        self.client = HTTP(
            testnet=False,  # 항상 False (testnet과 demo는 다름)
            api_key=api_key,
            api_secret=api_secret
        )

        # 데모 모드일 때는 base_url을 수동으로 변경
        if is_demo:
            # pybit 클라이언트의 내부 URL을 데모 URL로 변경
            self.client.endpoint = "https://api-demo.bybit.com"

        logger.info(f"BybitV5Client initialized: mode={'Demo' if is_demo else 'Real'}, URL={self.base_url}")
    
    def test_connection(self):
        """
        API 연결 테스트
        
        Returns:
            연결 성공 여부
        """
        try:
            result = self.client.get_wallet_balance(accountType="UNIFIED")
            if result.get('retCode') == 0:
                logger.info("API connection test successful")
                return True
            else:
                logger.error(f"API connection test failed: {result.get('retMsg')}")
                return False
        except Exception as e:
            logger.error(f"API connection test error: {e}")
            return False
    
    def get_wallet_balance(self):
        """
        지갑 잔고 조회 (오류 시 명확한 표시 후 정지)
        
        Returns:
            지갑 잔고 정보
        """
        try:
            result = self.client.get_wallet_balance(accountType="UNIFIED")
            if result.get('retCode') == 0:
                balances = result.get('result', {}).get('list', [])
                if balances:
                    return balances[0]
                return {}
            else:
                error_msg = f"Wallet balance API Error (Code: {result.get('retCode')}): {result.get('retMsg')}"
                logger.error(error_msg)
                raise Exception(error_msg)
        except Exception as e:
            logger.error(f"Critical Error getting wallet balance: {e}")
            raise
    
    def get_wallet_coin_balance(self, coin="USDT"):
        """
        특정 코인 잔고 조회
        
        Args:
            coin: 코인 심볼 (기본값: USDT)
            
        Returns:
            코인 잔고 정보
        """
        try:
            wallet = self.get_wallet_balance()
            if not wallet:
                return 0
            
            coins = wallet.get('coin', [])
            for coin_data in coins:
                if coin_data.get('coin') == coin:
                    return float(coin_data.get('walletBalance', 0))            
            return 0
        except Exception as e:
            logger.error(f"Error getting coin balance: {e}")
            return 0
    
    def get_ticker_data(self, symbol):
        """
        심볼 시세 정보 조회 (무한 재귀 방지)
        
        Args:
            symbol: 거래 심볼 (예: BTCUSDT)
            
        Returns:
            시세 정보
        """
        try:
            result = self.client.get_tickers(category="linear", symbol=symbol)
            if result.get('retCode') == 0:
                tickers = result.get('result', {}).get('list', [])
                if tickers:
                    return tickers[0]
                return {}
            else:
                error_msg = f"Failed to get ticker (Code: {result.get('retCode')}): {result.get('retMsg')}"
                logger.error(error_msg)
                raise Exception(error_msg)
        except Exception as e:
            logger.error(f"Critical Error getting ticker: {e}")
            raise
    
    def get_current_price(self, symbol):
        """
        현재가 조회 (무한 재귀 방지)
        
        Args:
            symbol: 거래 심볼 (예: BTCUSDT)
            
        Returns:
            현재가
        """
        try:
            ticker = self.get_ticker_data(symbol)  # 직접 호출로 재귀 방지
            if ticker and 'lastPrice' in ticker:
                return float(ticker['lastPrice'])
            else:
                error_msg = f"No price data available for {symbol}"
                logger.error(error_msg)
                raise Exception(error_msg)
        except Exception as e:
            logger.error(f"Critical Error getting current price: {e}")
            raise
    
    def place_order(self, symbol, side, order_type, qty=None, size=None, price=None, time_in_force="GoodTillCancel", position_idx=0):
        """
        주문 실행

        Args:
            symbol: 거래 심볼 (예: BTCUSDT)
            side: 주문 방향 (Buy, Sell)
            order_type: 주문 타입 (Market, Limit)
            qty: 수량 (우선순위)
            size: 수량 (호환성을 위해 추가)
            price: 가격 (지정가 주문 시)
            time_in_force: 주문 유효 기간

        Returns:
            주문 결과
        """
        try:
            # qty와 size 파라미터 호환성 처리
            if qty is None and size is not None:
                qty = size
            elif qty is None and size is None:
                raise ValueError("qty 또는 size 파라미터가 필요합니다")
            params = {
                "category": "linear",
                "symbol": symbol,
                "side": side,
                "orderType": order_type,
                "qty": str(qty),
                "timeInForce": time_in_force,
                "positionIdx": position_idx  # Hedge Mode 지원을 위한 position index
            }
            
            if order_type == "Limit" and price is not None:
                params["price"] = str(price)
            
            result = self.client.place_order(**params)
            
            if result.get('retCode') == 0:
                order_result = result.get('result', {})
                logger.info(f"Order placed successfully: {order_result}")
                return order_result
            else:
                logger.error(f"Failed to place order: {result.get('retMsg')}")
                return None
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    def cancel_order(self, order_id, symbol):
        """
        주문 취소
        
        Args:
            order_id: 주문 ID
            symbol: 거래 심볼
            
        Returns:
            취소 성공 여부
        """
        try:
            result = self.client.cancel_order(
                category="linear",
                symbol=symbol,
                orderId=order_id
            )
            
            if result.get('retCode') == 0:
                logger.info(f"Order cancelled: {order_id}")
                return True
            else:
                logger.error(f"Failed to cancel order: {result.get('retMsg')}")
                return False
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return False
    
    def get_positions(self, symbol=None):
        """
        포지션 조회
        
        Args:
            symbol: 거래 심볼 (None인 경우 모든 포지션 조회)
            
        Returns:
            포지션 목록
        """
        try:
            params = {"category": "linear"}
            if symbol:
                params["symbol"] = symbol
            
            result = self.client.get_positions(**params)
            
            if result.get('retCode') == 0:
                positions = result.get('result', {}).get('list', [])
                return positions
            else:
                logger.error(f"Failed to get positions: {result.get('retMsg')}")
                return []
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []
    
    def get_active_position(self, symbol):
        """
        특정 심볼의 활성 포지션 조회
        
        Args:
            symbol: 거래 심볼
            
        Returns:
            활성 포지션 정보
        """
        positions = self.get_positions(symbol)
        
        for position in positions:
            size = float(position.get('size', 0))
            if size != 0:
                return position
        
        return None
    
    def close_position(self, symbol, side=None):
        """
        포지션 청산
        
        Args:
            symbol: 거래 심볼
            side: 청산할 포지션 방향 (Buy, Sell, None=모두)
            
        Returns:
            청산 성공 여부
        """
        try:
            position = self.get_active_position(symbol)
            if not position:
                logger.info(f"No active position to close for {symbol}")
                return True
            
            position_side = position.get('side')
            size = abs(float(position.get('size', 0)))
            
            if size == 0:
                return True
            
            # 방향이 지정된 경우 해당 방향만 청산
            if side and position_side != side:
                return True
            
            # 청산 주문 방향 결정 (Buy 포지션은 Sell로 청산, Sell 포지션은 Buy로 청산)
            close_side = "Sell" if position_side == "Buy" else "Buy"
            
            result = self.place_order(
                symbol=symbol,
                side=close_side,
                order_type="Market",
                qty=size
            )
            
            if result:
                logger.info(f"Position closed for {symbol}")
                return True
            else:
                logger.error(f"Failed to close position for {symbol}")
                return False
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return False
    
    def set_leverage(self, symbol, leverage):
        """
        레버리지 설정
        
        Args:
            symbol: 거래 심볼
            leverage: 레버리지 배수
            
        Returns:
            설정 성공 여부
        """
        try:
            # 문자열에서 숫자만 추출 (예: "5X" -> 5)
            if isinstance(leverage, str):
                leverage = int(''.join(filter(str.isdigit, leverage)))
            
            result = self.client.set_leverage(
                category="linear",
                symbol=symbol,
                buyLeverage=str(leverage),
                sellLeverage=str(leverage)
            )
            
            if result.get('retCode') == 0:
                logger.info(f"Leverage set to {leverage}x for {symbol}")
                return True
            else:
                logger.error(f"Failed to set leverage: {result.get('retMsg')}")
                return False
        except Exception as e:
            logger.error(f"Error setting leverage: {e}")
            return False
    
    def calculate_pnl(self, position):
        """
        포지션 손익 계산
        
        Args:
            position: 포지션 정보
            
        Returns:
            손익률(%)
        """
        if not position:
            return 0
        
        try:
            # 포지션 정보 추출
            symbol = position.get('symbol')
            side = position.get('side')
            entry_price = float(position.get('avgPrice', 0))
            size = float(position.get('size', 0))
            
            if entry_price == 0 or size == 0:
                return 0
            
            # 현재가 조회
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                return 0
            
            # 손익률 계산
            if side == "Buy":  # 롱 포지션
                pnl_percent = ((current_price - entry_price) / entry_price) * 100
            else:  # 숏 포지션
                pnl_percent = ((entry_price - current_price) / entry_price) * 100
            
            return pnl_percent
        except Exception as e:
            logger.error(f"Error calculating PnL: {e}")
            return 0
    
    def get_order_history(self, symbol, limit=50):
        """
        주문 내역 조회
        
        Args:
            symbol: 거래 심볼
            limit: 조회 개수
            
        Returns:
            주문 내역 목록
        """
        try:
            result = self.client.get_order_history(
                category="linear",
                symbol=symbol,
                limit=limit
            )
            
            if result.get('retCode') == 0:
                orders = result.get('result', {}).get('list', [])
                return orders
            else:
                logger.error(f"Failed to get order history: {result.get('retMsg')}")
                return []
        except Exception as e:
            logger.error(f"Error getting order history: {e}")
            return []

    def get_balance(self):
        """
        사용 가능한 잔고 조회 (trading_logic에서 사용)

        Returns:
            사용 가능한 USDT 잔고
        """
        try:
            balance = self.get_wallet_coin_balance("USDT")
            return {'available_balance': balance}
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return {'available_balance': 0}

    def get_ticker(self, symbol):
        """
        티커 정보 조회 (trading_logic에서 사용)

        Args:
            symbol: 거래 심볼

        Returns:
            티커 정보
        """
        try:
            ticker_data = self.get_ticker_data(symbol)
            if ticker_data:
                return {
                    'symbol': ticker_data.get('symbol'),
                    'lastPrice': float(ticker_data.get('lastPrice', 0)),
                    'price': float(ticker_data.get('lastPrice', 0)),  # 호환성을 위해 추가
                    'volume24h': float(ticker_data.get('volume24h', 0)),
                    'priceChangePercent': float(ticker_data.get('price24hPcnt', 0)) * 100
                }
            return None
        except Exception as e:
            logger.error(f"Error getting ticker: {e}")
            return None

    def get_ticker_price(self, symbol):
        """
        현재 가격 조회 (trading_logic에서 사용)

        Args:
            symbol: 거래 심볼

        Returns:
            가격 정보
        """
        try:
            current_price = self.get_current_price(symbol)
            return {'price': current_price} if current_price > 0 else None
        except Exception as e:
            logger.error(f"Error getting ticker price: {e}")
            return None

    def place_market_order(self, symbol, side, size):
        """
        시장가 주문 실행 (trading_logic에서 사용하는 간소화된 인터페이스)

        Args:
            symbol: 거래 심볼
            side: 주문 방향 (LONG, SHORT, BUY, SELL)
            size: 수량

        Returns:
            주문 결과
        """
        try:
            # 방향 변환
            if side in ['LONG', 'BUY']:
                order_side = 'Buy'
            elif side in ['SHORT', 'SELL']:
                order_side = 'Sell'
            else:
                order_side = side

            # 시장가 주문 실행
            result = self.place_order(
                symbol=symbol,
                side=order_side,
                order_type='Market',
                qty=size
            )

            if result:
                return {
                    'success': True,
                    'order_id': result.get('orderId'),
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'Order placement failed'
                }

        except Exception as e:
            logger.error(f"Error in market order: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def place_limit_order(self, symbol, side, size, price):
        """
        지정가 주문 실행 (trading_logic에서 사용하는 간소화된 인터페이스)

        Args:
            symbol: 거래 심볼
            side: 주문 방향 (LONG, SHORT, BUY, SELL)
            size: 수량
            price: 지정가 가격

        Returns:
            주문 결과
        """
        try:
            # 방향 변환
            if side in ['LONG', 'BUY']:
                order_side = 'Buy'
            elif side in ['SHORT', 'SELL']:
                order_side = 'Sell'
            else:
                order_side = side

            # 🟢 One-Way Mode에서 Position Index 설정 (항상 0)
            position_idx = 0  # One-Way Mode: 단일 포지션만 지원

            # 지정가 주문 실행
            result = self.place_order(
                symbol=symbol,
                side=order_side,
                order_type='Limit',
                qty=size,
                price=price,
                time_in_force='GTC',  # Good Till Cancelled
                position_idx=position_idx
            )

            if result:
                return {
                    'success': True,
                    'order_id': result.get('orderId'),
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'Limit order placement failed'
                }

        except Exception as e:
            logger.error(f"Error in limit order: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def close_position_by_side(self, symbol, side, size):
        """
        포지션 종료 (trading_logic에서 사용하는 간소화된 인터페이스)

        Args:
            symbol: 거래 심볼
            side: 종료할 포지션 방향
            size: 수량

        Returns:
            종료 결과
        """
        try:
            # 종료 주문 방향 결정
            if side in ['LONG', 'BUY']:
                close_side = 'Sell'
            elif side in ['SHORT', 'SELL']:
                close_side = 'Buy'
            else:
                close_side = 'Sell' if side == 'Buy' else 'Buy'

            # 🟢 One-Way Mode: 포지션 인덱스 항상 0
            position_idx = 0  # One-Way Mode: 단일 포지션만 지원

            result = self.place_order(
                symbol=symbol,
                side=close_side,
                order_type='Market',
                qty=size,
                position_idx=position_idx
            )

            if result:
                return {
                    'success': True,
                    'order_id': result.get('orderId'),
                    'result': result
                }
            else:
                return {
                    'success': False,
                    'error': 'Position close failed'
                }

        except Exception as e:
            logger.error(f"Error in close position: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_account_info(self):
        """
        계정 정보 조회

        Returns:
            계정 정보
        """
        try:
            wallet_balance = self.get_wallet_balance()
            usdt_balance = self.get_wallet_coin_balance("USDT")

            return {
                'wallet_balance': wallet_balance,
                'usdt_balance': usdt_balance,
                'is_demo': self.is_demo,
                'connection_status': 'connected' if self.test_connection() else 'disconnected'
            }

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {
                'error': str(e),
                'is_demo': self.is_demo,
                'connection_status': 'error'
            }

    def get_trading_symbols(self):
        """
        거래 가능한 심볼 목록 조회

        Returns:
            심볼 목록
        """
        try:
            result = self.client.get_instruments_info(category="linear")

            if result.get('retCode') == 0:
                instruments = result.get('result', {}).get('list', [])
                symbols = []

                # USDT 페어만 필터링
                for instrument in instruments:
                    symbol = instrument.get('symbol', '')
                    if symbol.endswith('USDT') and instrument.get('status') == 'Trading':
                        symbols.append({
                            'symbol': symbol,
                            'base_coin': instrument.get('baseCoin'),
                            'quote_coin': instrument.get('quoteCoin'),
                            'min_order_qty': instrument.get('lotSizeFilter', {}).get('minOrderQty'),
                            'max_order_qty': instrument.get('lotSizeFilter', {}).get('maxOrderQty')
                        })

                return symbols[:50]  # 상위 50개만 반환
            else:
                logger.error(f"Failed to get trading symbols: {result.get('retMsg')}")
                return []

        except Exception as e:
            logger.error(f"Error getting trading symbols: {e}")
            return []

    def get_server_time(self):
        """
        서버 시간 조회

        Returns:
            서버 시간
        """
        try:
            result = self.client.get_server_time()

            if result.get('retCode') == 0:
                server_time = result.get('result', {}).get('timeSecond')
                return {
                    'server_time': server_time,
                    'server_time_ms': result.get('result', {}).get('timeNano'),
                    'local_time': int(time.time())
                }
            else:            return {}

        except Exception as e:
            logger.error(f"Error getting server time: {e}")
            return {}