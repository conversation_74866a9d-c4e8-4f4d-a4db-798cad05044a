^D:\AUGMENT-PROJECTS\OPENSYSTEMS_BOT_V3\BUILD\WINDOWS\X64\CMAKEFILES\7E7E802F294D7E9EB8ECEF17C849F012\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/augment-projects/OPENSYSTEMS_BOT_V3/windows -BD:/augment-projects/OPENSYSTEMS_BOT_V3/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/augment-projects/OPENSYSTEMS_BOT_V3/build/windows/x64/opensystems_bot_mobile.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
