#!/usr/bin/env python3
# API 키 상태 확인 스크립트
import sqlite3
import os

def check_api_keys():
    """데이터베이스에서 API 키 상태 확인"""
    
    db_path = 'backend/db/opensystems_bot.db'
    
    if not os.path.exists(db_path):
        print(f'❌ 데이터베이스 파일을 찾을 수 없습니다: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 설정 테이블 구조 확인
        cursor.execute("PRAGMA table_info(settings)")
        columns = cursor.fetchall()

        print('📋 설정 테이블 구조:')
        print('=' * 50)
        for col in columns:
            print(f'{col[1]:20} {col[2]:15}')

        print('\n📊 현재 설정 데이터:')
        print('=' * 50)

        # 모든 컬럼 조회
        cursor.execute("SELECT * FROM settings WHERE user_id = 1")
        
        result = cursor.fetchone()

        if result:
            print('데이터:', result)
        else:
            print('❌ 사용자 설정을 찾을 수 없습니다')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 오류 발생: {e}')

if __name__ == '__main__':
    check_api_keys()
