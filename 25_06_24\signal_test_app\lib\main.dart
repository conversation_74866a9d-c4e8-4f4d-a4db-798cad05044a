import 'package:flutter/material.dart';
import 'package:signalr_netcore/signalr_client.dart';
import 'dart:async';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Signal Test App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final List<String> _logs = [];
  late HubConnection _hubConnection;
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _initializeSignalR();
  }

  void _initializeSignalR() {
    _hubConnection = HubConnectionBuilder()
        .withUrl('http://localhost:5000/signalr',
            options: HttpConnectionOptions(
              skipNegotiation: true,
              transport: HttpTransportType.WebSockets,
            ))
        .withAutomaticReconnect()
        .build();

    _hubConnection.on('SendSignal', (message) {
      setState(() {
        _logs.add(message.toString());
        if (_logs.length > 100) _logs.removeAt(0);
      });
    });
    _hubConnection.onclose(({Exception? error}) {
      setState(() {
        _isConnected = false;
      });
      // print('SignalR 연결이 끊어졌습니다: $error'); // 개발용 - 프로덕션에서는 로거 사용
      // 연결이 끊어지면 3초 후 재연결 시도
      Future.delayed(const Duration(seconds: 3), () {
        _hubConnection.start()?.then((_) {
          setState(() {
            _isConnected = true;
          });
          // print('SignalR 재연결 성공'); // 개발용 - 프로덕션에서는 로거 사용
        }).catchError((err) {
          // print('SignalR 재연결 실패: $err'); // 개발용 - 프로덕션에서는 로거 사용
        });
      });
    });

    _hubConnection.start()?.then((_) {
      setState(() {
        _isConnected = true;
      });
      // print('SignalR 연결 성공'); // 개발용 - 프로덕션에서는 로거 사용
    }).catchError((err) {
      // print('SignalR 연결 실패: $err'); // 개발용 - 프로덕션에서는 로거 사용
    });
  }

  @override
  void dispose() {
    _hubConnection.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('Signal Test App'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 카드 1
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Container(
                height: 100,
                padding: const EdgeInsets.all(16.0),
                child: const Center(
                  child: Text('카드 1'),
                ),
              ),
            ),
            // 카드 2
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Container(
                height: 100,
                padding: const EdgeInsets.all(16.0),
                child: const Center(
                  child: Text('카드 2'),
                ),
              ),
            ),
            // 카드 3
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Container(
                height: 100,
                padding: const EdgeInsets.all(16.0),
                child: const Center(
                  child: Text('카드 3'),
                ),
              ),
            ),
            // 카드 4 (로그 표시)
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Container(
                height: 300,
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '실시간 로그 (${_isConnected ? '연결됨' : '연결 끊김'})',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2.0),
                            child: Text(
                              _logs[index],
                              style: const TextStyle(fontSize: 12),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
