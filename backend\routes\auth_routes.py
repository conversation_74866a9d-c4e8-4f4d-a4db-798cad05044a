# 인증 관련 API 라우트
# routes/auth_routes.py
import logging
from datetime import datetime, timezone
from flask import Blueprint, jsonify, request
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity

from database import db_session
from models.user import User
from models.setting import Setting
from utils.helpers import is_valid_email

logger = logging.getLogger("opensystems_bot")

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """사용자 로그인"""
    try:
        data = request.get_json()

        if not data or not data.get('username') or not data.get('password'):
            return jsonify({'error': '사용자명과 비밀번호를 입력해주세요'}), 400

        username = data['username']
        password = data['password']

        # 사용자 조회 (username 또는 email로 로그인 가능)
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        if not user:
            return jsonify({'error': '존재하지 않는 사용자입니다'}), 401

        if not user.is_active:
            return jsonify({'error': '비활성화된 계정입니다'}), 401

        if not user.check_password(password):
            return jsonify({'error': '비밀번호가 올바르지 않습니다'}), 401

        # 마지막 로그인 시간 업데이트
        user.update_last_login()
        db_session.commit()

        # JWT 토큰 생성
        access_token = create_access_token(identity=user.username)

        logger.info(f"사용자 로그인 성공: {user.username}")

        return jsonify({
            'success': True,
            'message': '로그인 성공',
            'access_token': access_token,
            'user': user.to_dict(include_admin=True)
        }), 200

    except Exception as e:
        logger.error(f"로그인 오류: {e}")
        return jsonify({'error': '로그인 처리 중 오류가 발생했습니다'}), 500

@auth_bp.route('/register', methods=['POST'])
def register():
    """사용자 회원가입"""
    try:
        data = request.get_json()

        # 디버깅: 받은 데이터 로깅
        logger.info(f"간단 로그인 요청 데이터: {data}")

        if not data:
            logger.error("로그인 요청 데이터가 없음")
            return jsonify({'error': '요청 데이터가 없습니다'}), 400

        # 필수 필드 확인
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                logger.error(f"필수 필드 누락: {field}")
                return jsonify({'error': f'{field}는 필수 입력 항목입니다'}), 400

        username = data['username'].strip()
        email = data['email'].strip().lower()
        password = data['password']
        confirm_password = data.get('confirmPassword', '')

        # 디버깅: 처리된 데이터 로깅
        logger.info(f"처리된 데이터 - username: '{username}', email: '{email}', password 길이: {len(password)}, confirmPassword: '{confirm_password}'")

        # 유효성 검사
        if len(username) < 3:
            logger.error(f"사용자명 길이 부족: {len(username)}자")
            return jsonify({'error': '사용자명은 3자 이상이어야 합니다'}), 400

        if not is_valid_email(email):
            logger.error(f"이메일 형식 오류: {email}")
            return jsonify({'error': '올바른 이메일 형식이 아닙니다'}), 400

        if len(password) < 6:
            logger.error(f"비밀번호 길이 부족: {len(password)}자")
            return jsonify({'error': '비밀번호는 6자 이상이어야 합니다'}), 400

        # confirmPassword가 있는 경우에만 검증 (선택사항)
        if confirm_password and password != confirm_password:
            logger.error(f"비밀번호 불일치: password='{password}', confirmPassword='{confirm_password}'")
            return jsonify({'error': '비밀번호가 일치하지 않습니다'}), 400

        # 중복 확인
        existing_user = User.query.filter(
            (User.username == username) | (User.email == email)
        ).first()

        if existing_user:
            if existing_user.username == username:
                return jsonify({'error': '이미 사용 중인 사용자명입니다'}), 400
            else:
                return jsonify({'error': '이미 사용 중인 이메일입니다'}), 400

        # 새 사용자 생성
        new_user = User(
            username=username,
            email=email,
            password=password
        )

        db_session.add(new_user)
        db_session.flush()  # ID 생성을 위해 flush

        # 기본 설정 생성
        default_setting = Setting(user_id=new_user.id)
        db_session.add(default_setting)

        db_session.commit()

        logger.info(f"새 사용자 등록: {username}")

        return jsonify({
            'success': True,
            'message': '회원가입이 완료되었습니다. 로그인해주세요.',
            'user': new_user.to_dict()
        }), 201

    except Exception as e:
        logger.error(f"회원가입 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '회원가입 처리 중 오류가 발생했습니다'}), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """사용자 프로필 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        return jsonify({
            'user': user.to_dict(include_admin=True),
            'setting': user.setting.to_dict() if user.setting else None
        }), 200

    except Exception as e:
        logger.error(f"프로필 조회 오류: {e}")
        return jsonify({'error': '프로필 조회 중 오류가 발생했습니다'}), 500

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """사용자 프로필 업데이트"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 데이터가 없습니다'}), 400

        updated_fields = []

        # 이메일 업데이트
        if 'email' in data:
            new_email = data['email'].strip().lower()
            if not is_valid_email(new_email):
                return jsonify({'error': '올바른 이메일 형식이 아닙니다'}), 400

            # 이메일 중복 확인
            existing_user = User.query.filter(
                (User.email == new_email) & (User.id != user.id)
            ).first()

            if existing_user:
                return jsonify({'error': '이미 사용 중인 이메일입니다'}), 400

            user.email = new_email
            updated_fields.append('email')

        # 비밀번호 업데이트
        if 'password' in data and 'current_password' in data:
            current_password = data['current_password']
            new_password = data['password']

            if not user.check_password(current_password):
                return jsonify({'error': '현재 비밀번호가 올바르지 않습니다'}), 400

            if len(new_password) < 6:
                return jsonify({'error': '새 비밀번호는 6자 이상이어야 합니다'}), 400

            user.set_password(new_password)
            updated_fields.append('password')

        if updated_fields:
            user.updated_at = datetime.now(timezone.utc)
            db_session.commit()

            logger.info(f"프로필 업데이트: {user.username}, 필드: {updated_fields}")

            return jsonify({
                'success': True,
                'message': '프로필이 업데이트되었습니다',
                'updated_fields': updated_fields,
                'user': user.to_dict(include_admin=True)
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '업데이트할 내용이 없습니다'
            }), 400

    except Exception as e:
        logger.error(f"프로필 업데이트 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '프로필 업데이트 중 오류가 발생했습니다'}), 500

@auth_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_token():
    """토큰 유효성 검증"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user or not user.is_active:
            return jsonify({'valid': False, 'error': '유효하지 않은 사용자입니다'}), 401

        return jsonify({
            'valid': True,
            'user': user.to_dict(include_admin=True)
        }), 200

    except Exception as e:
        logger.error(f"토큰 검증 오류: {e}")
        return jsonify({'valid': False, 'error': '토큰 검증 중 오류가 발생했습니다'}), 500

@auth_bp.route('/simple-login', methods=['POST'])
def simple_login():
    """간단한 로그인 (세션/JWT 없이)"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '요청 데이터가 없습니다'}), 400

        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': '사용자명과 비밀번호를 입력해주세요'}), 400

        # 사용자 조회 (username 또는 email로 로그인 가능)
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        if not user:
            return jsonify({'error': '존재하지 않는 사용자입니다'}), 401

        if not user.is_active:
            return jsonify({'error': '비활성화된 계정입니다'}), 401

        if not user.check_password(password):
            return jsonify({'error': '비밀번호가 올바르지 않습니다'}), 401

        # 마지막 로그인 시간 업데이트
        user.update_last_login()
        db_session.commit()

        logger.info(f"간단 로그인 성공: {user.username}")

        return jsonify({
            'success': True,
            'username': user.username,
            'user': user.to_dict(include_admin=True)
        }), 200

    except Exception as e:
        logger.error(f"간단 로그인 오류: {e}")
        return jsonify({'error': '로그인 처리 중 오류가 발생했습니다'}), 500

@auth_bp.route('/profile-simple', methods=['GET'])
def get_profile_simple():
    """사용자 프로필 조회 (세션 기반)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        username = data.get('username')
        if not username:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        return jsonify({
            'user': user.to_dict(include_admin=True),
            'setting': user.setting.to_dict() if user.setting else None
        }), 200

    except Exception as e:
        logger.error(f"프로필 조회 오류 (간단): {e}")
        return jsonify({'error': '프로필 조회 중 오류가 발생했습니다'}), 500

@auth_bp.route('/profile-simple', methods=['PUT'])
def update_profile_simple():
    """사용자 프로필 업데이트 (세션 기반)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 데이터가 없습니다'}), 400

        username = data.get('username')
        if not username:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        updated_fields = []

        # 이메일 업데이트
        if 'email' in data and data['email']:
            new_email = data['email'].strip().lower()
            if not is_valid_email(new_email):
                return jsonify({'error': '올바른 이메일 형식이 아닙니다'}), 400

            # 이메일 중복 확인 (자신 제외)
            existing_user = User.query.filter(
                (User.email == new_email) & (User.id != user.id)
            ).first()

            if existing_user:
                return jsonify({'error': '이미 사용 중인 이메일입니다'}), 400

            user.email = new_email
            updated_fields.append('email')

        # 비밀번호 업데이트
        if 'new_password' in data and data['new_password']:
            current_password = data.get('current_password')
            new_password = data['new_password']

            if not current_password:
                return jsonify({'error': '현재 비밀번호를 입력해주세요'}), 400

            if not user.check_password(current_password):
                return jsonify({'error': '현재 비밀번호가 올바르지 않습니다'}), 400

            if len(new_password) < 6:
                return jsonify({'error': '새 비밀번호는 6자 이상이어야 합니다'}), 400

            user.set_password(new_password)
            updated_fields.append('password')

        if updated_fields:
            from datetime import datetime, timezone
            user.updated_at = datetime.now(timezone.utc)
            db_session.commit()

            logger.info(f"프로필 업데이트 (간단): {user.username}, 필드: {updated_fields}")

            return jsonify({
                'success': True,
                'message': '프로필이 업데이트되었습니다',
                'updated_fields': updated_fields,
                'user': user.to_dict(include_admin=True)
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': '업데이트할 내용이 없습니다'
            }), 400

    except Exception as e:
        logger.error(f"프로필 업데이트 오류 (간단): {e}")
        db_session.rollback()
        return jsonify({'error': '프로필 업데이트 중 오류가 발생했습니다'}), 500
