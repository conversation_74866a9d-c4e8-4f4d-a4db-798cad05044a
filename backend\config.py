# config.py
import os
from datetime import timedelta

class Config:
    """애플리케이션 환경 설정 클래스"""
    
    # 기본 설정
    SECRET_KEY = os.environ.get('SECRET_KEY', 'opensystems_secret_key')
    DEBUG = os.environ.get('FLASK_ENV') == 'development'
    ENV = os.environ.get('FLASK_ENV', 'production')
    
    # 데이터베이스 설정
    DB_PATH = os.environ.get('DB_PATH', 'db')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL', f'sqlite:///{DB_PATH}/opensystems_bot.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # JWT 설정
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', SECRET_KEY)
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=12)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    
    # 암호화 설정
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', SECRET_KEY)
    ENCRYPTION_SALT = os.environ.get('ENCRYPTION_SALT', 'opensystems_salt')
    
    # Bybit API 설정
    BYBIT_DEMO_URL = 'https://api-demo.bybit.com'
    BYBIT_REAL_URL = 'https://api.bybit.com'
    
    # SignalR 설정
    SIGNAL_URL = os.environ.get('SIGNAL_URL', 'https://signalr.example.com/trading')
    
    # 로깅 설정
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_DIR = os.environ.get('LOG_DIR', 'logs')
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10 MB
    LOG_BACKUP_COUNT = 5
    
    # 타임아웃 설정
    REQUEST_TIMEOUT = 10  # 초
    
    # 세션 설정
    SESSION_TYPE = 'filesystem'
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # CORS 설정
    CORS_ORIGINS = ['*']
    
    # 어드민 계정 설정
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '<EMAIL>')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')

class DevelopmentConfig(Config):
    """개발 환경 설정"""
    DEBUG = True
    ENV = 'development'
    LOG_LEVEL = 'DEBUG'

class TestingConfig(Config):
    """테스트 환경 설정"""
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=5)
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """프로덕션 환경 설정"""
    DEBUG = False
    ENV = 'production'
    # 프로덕션 환경에서는 환경 변수로부터 모든 설정을 가져와야 함
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY')
    ENCRYPTION_SALT = os.environ.get('ENCRYPTION_SALT')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')

# 환경 변수에 따라 설정 선택
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}

# 현재 활성화된 설정
active_config = config_by_name.get(os.environ.get('FLASK_ENV', 'production'), ProductionConfig)