#!/usr/bin/env python3
"""
SQLite 데이터베이스 생성 스크립트
"""
import sqlite3
import os
from datetime import datetime
from werkzeug.security import generate_password_hash

def create_database():
    # 데이터베이스 파일 경로
    db_path = 'backend/db/opensystems_bot.db'
    
    # 기존 파일이 있으면 삭제
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f'기존 데이터베이스 파일 삭제: {db_path}')
    
    # SQLite 연결
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f'SQLite 데이터베이스 생성: {db_path}')
    
    # 1. users 테이블 생성
    cursor.execute('''
    CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(128) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        is_admin BOOLEAN DEFAULT 0,
        admin_level INTEGER DEFAULT 0,
        created_at DATETIME,
        updated_at DATETIME,
        last_login DATETIME
    )
    ''')
    print('users 테이블 생성 완료')
    
    # 2. settings 테이블 생성
    cursor.execute('''
    CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        symbol VARCHAR(20) DEFAULT 'BTCUSDT',
        leverage INTEGER DEFAULT 5,
        investment_ratio FLOAT DEFAULT 0.25,
        is_demo BOOLEAN DEFAULT 1,
        demo_api_key_encrypted TEXT,
        demo_api_secret_encrypted TEXT,
        real_api_key_encrypted TEXT,
        real_api_secret_encrypted TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    print('settings 테이블 생성 완료')
    
    # 3. logs 테이블 생성
    cursor.execute('''
    CREATE TABLE logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action VARCHAR(50),
        details TEXT,
        timestamp DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    print('logs 테이블 생성 완료')
    
    # 4. signals 테이블 생성
    cursor.execute('''
    CREATE TABLE signals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        signal_type VARCHAR(20),
        message TEXT,
        timestamp DATETIME,
        processed BOOLEAN DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    print('signals 테이블 생성 완료')
    
    # 5. positions 테이블 생성
    cursor.execute('''
    CREATE TABLE positions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        symbol VARCHAR(20),
        side VARCHAR(10),
        size FLOAT,
        entry_price FLOAT,
        current_price FLOAT,
        pnl FLOAT,
        status VARCHAR(20),
        created_at DATETIME,
        updated_at DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')
    print('positions 테이블 생성 완료')
    
    # 6. trades 테이블 생성
    cursor.execute('''
    CREATE TABLE trades (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        position_id INTEGER,
        symbol VARCHAR(20),
        side VARCHAR(10),
        size FLOAT,
        price FLOAT,
        fee FLOAT,
        pnl FLOAT,
        trade_type VARCHAR(20),
        timestamp DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (position_id) REFERENCES positions (id)
    )
    ''')
    print('trades 테이블 생성 완료')
    
    # 어드민 사용자 생성
    admin_password_hash = generate_password_hash('admin123')
    current_time = datetime.utcnow().isoformat()
    
    cursor.execute('''
    INSERT INTO users (username, email, password_hash, is_active, is_admin, admin_level, created_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', ('<EMAIL>', '<EMAIL>', admin_password_hash, 1, 1, 3, current_time))
    
    admin_user_id = cursor.lastrowid
    print(f'어드민 사용자 생성 완료 (ID: {admin_user_id})')
    
    # 어드민 사용자 기본 설정 생성
    cursor.execute('''
    INSERT INTO settings (user_id, symbol, leverage, investment_ratio, is_demo, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (admin_user_id, 'BTCUSDT', 5, 0.25, 1, current_time))
    
    print('어드민 사용자 기본 설정 생성 완료')
    
    # 변경사항 저장 및 연결 종료
    conn.commit()
    conn.close()
    
    print(f'SQLite 데이터베이스 생성 완료: {db_path}')
    print('어드민 계정: <EMAIL> / admin123')

if __name__ == '__main__':
    create_database()
