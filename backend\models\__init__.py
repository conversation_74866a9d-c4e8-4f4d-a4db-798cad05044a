# Data Models
# models/__init__.py

# 모든 모델 import
from .user import User
from .setting import Setting
from .log import Log
from .signal import Signal
from .position import Position
from .trade import Trade
from .profit_tracking import ProfitTracking

# 모델 관계 정리를 위한 함수
def setup_model_relationships():
    """
    모델 간 관계 설정 확인 및 정리

    관계 구조:
    User (1) -> Setting (1)
    User (1) -> Log (N)
    User (1) -> Signal (N)
    User (1) -> Position (N)
    User (1) -> Trade (N)
    Position (1) -> Trade (N)
    """

    # User 모델 관계 확인
    assert hasattr(User, 'setting'), "User.setting relationship missing"
    assert hasattr(User, 'logs'), "User.logs relationship missing"
    assert hasattr(User, 'signals'), "User.signals relationship missing"
    assert hasattr(User, 'positions'), "User.positions relationship missing"
    assert hasattr(User, 'trades'), "User.trades relationship missing"

    # Setting 모델 관계 확인
    assert hasattr(Setting, 'user'), "Setting.user relationship missing"

    # Log 모델 관계 확인
    assert hasattr(Log, 'user'), "Log.user relationship missing"

    # Signal 모델 관계 확인
    assert hasattr(Signal, 'user'), "Signal.user relationship missing"

    # Position 모델 관계 확인
    assert hasattr(Position, 'user'), "Position.user relationship missing"
    assert hasattr(Position, 'trades'), "Position.trades relationship missing"

    # Trade 모델 관계 확인
    assert hasattr(Trade, 'user'), "Trade.user relationship missing"
    assert hasattr(Trade, 'position'), "Trade.position relationship missing"

    print("✅ 모든 모델 관계가 올바르게 설정되었습니다")

# 편의를 위한 모델 목록
__all__ = [
    'User',
    'Setting',
    'Log',
    'Signal',
    'Position',
    'Trade',
    'ProfitTracking',
    'setup_model_relationships'
]
