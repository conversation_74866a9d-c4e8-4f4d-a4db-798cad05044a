﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{1840006A-C1AC-3CDD-861F-BB877745CA29}"
	ProjectSection(ProjectDependencies) = postProject
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67} = {E26ADC30-8E12-32E3-937E-82B4CD1F1B67}
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79} = {3F00E3D6-7974-3690-AC2E-1908E22AEA79}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{57E45EB6-E780-3568-9562-E05C1DE663FE}"
	ProjectSection(ProjectDependencies) = postProject
		{1840006A-C1AC-3CDD-861F-BB877745CA29} = {1840006A-C1AC-3CDD-861F-BB877745CA29}
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67} = {E26ADC30-8E12-32E3-937E-82B4CD1F1B67}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\flutter\flutter_assemble.vcxproj", "{FD491162-8ADC-33E1-B384-FFFFD26D96F2}"
	ProjectSection(ProjectDependencies) = postProject
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67} = {E26ADC30-8E12-32E3-937E-82B4CD1F1B67}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "..\flutter\flutter_wrapper_app.vcxproj", "{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}"
	ProjectSection(ProjectDependencies) = postProject
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67} = {E26ADC30-8E12-32E3-937E-82B4CD1F1B67}
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2} = {FD491162-8ADC-33E1-B384-FFFFD26D96F2}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "opensystems_bot_mobile", "opensystems_bot_mobile.vcxproj", "{3F00E3D6-7974-3690-AC2E-1908E22AEA79}"
	ProjectSection(ProjectDependencies) = postProject
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67} = {E26ADC30-8E12-32E3-937E-82B4CD1F1B67}
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2} = {FD491162-8ADC-33E1-B384-FFFFD26D96F2}
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5} = {91D14E1C-D5B6-37D0-A726-5E5F404D97C5}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Debug|x64.ActiveCfg = Debug|x64
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Debug|x64.Build.0 = Debug|x64
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Profile|x64.ActiveCfg = Profile|x64
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Profile|x64.Build.0 = Profile|x64
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Release|x64.ActiveCfg = Release|x64
		{1840006A-C1AC-3CDD-861F-BB877745CA29}.Release|x64.Build.0 = Release|x64
		{57E45EB6-E780-3568-9562-E05C1DE663FE}.Debug|x64.ActiveCfg = Debug|x64
		{57E45EB6-E780-3568-9562-E05C1DE663FE}.Profile|x64.ActiveCfg = Profile|x64
		{57E45EB6-E780-3568-9562-E05C1DE663FE}.Release|x64.ActiveCfg = Release|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Debug|x64.ActiveCfg = Debug|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Debug|x64.Build.0 = Debug|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Profile|x64.ActiveCfg = Profile|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Profile|x64.Build.0 = Profile|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Release|x64.ActiveCfg = Release|x64
		{E26ADC30-8E12-32E3-937E-82B4CD1F1B67}.Release|x64.Build.0 = Release|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Debug|x64.ActiveCfg = Debug|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Debug|x64.Build.0 = Debug|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Profile|x64.ActiveCfg = Profile|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Profile|x64.Build.0 = Profile|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Release|x64.ActiveCfg = Release|x64
		{FD491162-8ADC-33E1-B384-FFFFD26D96F2}.Release|x64.Build.0 = Release|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Debug|x64.ActiveCfg = Debug|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Debug|x64.Build.0 = Debug|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Profile|x64.ActiveCfg = Profile|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Profile|x64.Build.0 = Profile|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Release|x64.ActiveCfg = Release|x64
		{91D14E1C-D5B6-37D0-A726-5E5F404D97C5}.Release|x64.Build.0 = Release|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Debug|x64.ActiveCfg = Debug|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Debug|x64.Build.0 = Debug|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Profile|x64.ActiveCfg = Profile|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Profile|x64.Build.0 = Profile|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Release|x64.ActiveCfg = Release|x64
		{3F00E3D6-7974-3690-AC2E-1908E22AEA79}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7DA7DEC8-C639-3ABF-9D32-C443717BC6B3}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
