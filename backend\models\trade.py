# Trade Model
# models/trade.py
import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from database import Base
from utils.helpers import utc_now

class Trade(Base):
    """거래 모델"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    position_id = Column(Integer, ForeignKey('positions.id'), nullable=True)
    symbol = Column(String(20), nullable=False)
    side = Column(String(10), nullable=False)  # Buy, Sell
    price = Column(Float, nullable=False)
    size = Column(Float, nullable=False)
    fee = Column(Float, default=0.0)
    pnl = Column(Float, default=0.0)
    executed_at = Column(DateTime, default=datetime.datetime.utcnow)
    trade_type = Column(String(20))  # ENTRY, EXIT, FLIP
    reason = Column(String(50))  # SIGNAL, STOP_LOSS, DIRECTION_CHANGE
    order_id = Column(String(50), nullable=True)  # 거래소 주문 ID
    meta_data = Column(Text, nullable=True)  # 추가 메타데이터 (JSON)
    
    # 관계 설정
    user = relationship("User", back_populates="trades")
    position = relationship("Position", back_populates="trades")
    
    def __init__(self, user_id, symbol, side, price, size, **kwargs):
        """
        거래 초기화
        
        Args:
            user_id: 사용자 ID
            symbol: 거래 심볼
            side: 거래 방향 (Buy, Sell)
            price: 거래 가격
            size: 거래 수량
            **kwargs: 추가 파라미터
        """
        self.user_id = user_id
        self.symbol = symbol
        self.side = side
        self.price = price
        self.size = size
        self.executed_at = utc_now()
        
        # 추가 파라미터 설정
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def calculate_value(self):
        """거래 가치 계산"""
        return self.price * self.size
    
    def to_dict(self):
        """
        거래 정보를 딕셔너리로 변환
        
        Returns:
            거래 정보 딕셔너리
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'position_id': self.position_id,
            'symbol': self.symbol,
            'side': self.side,
            'price': self.price,
            'size': self.size,
            'fee': self.fee,
            'pnl': self.pnl,
            'value': self.calculate_value(),
            'executed_at': self.executed_at.isoformat() if self.executed_at else None,
            'trade_type': self.trade_type,
            'reason': self.reason,
            'order_id': self.order_id
        }
    
    def __repr__(self):
        return f"<Trade(id={self.id}, user_id={self.user_id}, symbol='{self.symbol}', side='{self.side}', price={self.price}, size={self.size})>"