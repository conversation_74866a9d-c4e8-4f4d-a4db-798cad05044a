# app.py
import os
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from flask_jwt_extended import JWTManager, get_jwt_identity, jwt_required
from flask_cors import CORS

# 내부 모듈 가져오기
from database import init_db, db_session
from config import Config
from utils.logger import setup_logger

# 모델 가져오기
from models.user import User
from models.setting import Setting
from models.log import Log
from models.trade import Trade

# 클라이언트 및 봇 모듈 가져오기
from api_client.bybit_v5_client import BybitV5Client
from bot_core.trading_logic import TradingLogic
from bot_core import SignalProcessor
from bot_core.mode_manager import ModeManager

# 라우트 가져오기
from routes.auth_routes import auth_bp
from routes.bot_routes import bot_bp
from routes.mode_routes import mode_bp
from routes.signal_routes import signal_bp
from routes.admin_routes import admin_bp

# 로거 설정
logger = setup_logger("opensystems_bot")

# 전역 변수 - 활성 신호 프로세서 및 매매 로직
signal_processors = {}
trading_logics = {}
mode_managers = {}

# Flask 앱 초기화
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)  # CORS 설정

# JWT 설정
jwt = JWTManager(app)

# 데이터베이스 초기화
init_db()

# 블루프린트 등록
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(bot_bp, url_prefix='/api/bot')
app.register_blueprint(mode_bp, url_prefix='/api/mode')
app.register_blueprint(signal_bp, url_prefix='/api/signal')
app.register_blueprint(admin_bp, url_prefix='/api/admin')

# 메인 API 엔드포인트
@app.route('/api/status', methods=['GET'])
@jwt_required()
def get_status():
    """시스템 상태 조회"""
    username = get_jwt_identity()
    user = User.query.filter_by(username=username).first()
    
    if not user:
        return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404
    
    # 사용자별 상태 조회
    if user.id in trading_logics:
        bot_status = trading_logics[user.id].get_status()
    else:
        bot_status = {"position_type": "IDLE", "status_text": "초기화되지 않음"}
    
    # 모드 정보 조회
    if user.id in mode_managers:
        mode_info = mode_managers[user.id].get_mode_info()
    else:
        mode_manager = ModeManager(user.id, BybitV5Client)
        mode_info = mode_manager.get_mode_info()
        mode_managers[user.id] = mode_manager
    
    # 신호 연결 상태 조회
    if user.id in signal_processors:
        signal_status = signal_processors[user.id].get_connection_status()
    else:
        signal_status = {"is_connected": False}
    
    return jsonify({
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'last_login': user.last_login.isoformat() if user.last_login else None
        },
        'bot': bot_status,
        'mode': mode_info,
        'signal': signal_status,
        'server_time': datetime.now().isoformat()
    }), 200

@app.route('/api/account', methods=['GET'])
@jwt_required()
def get_account_info():
    """계정 정보 조회"""
    username = get_jwt_identity()
    user = User.query.filter_by(username=username).first()
    
    if not user:
        return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404
    
    # 모드 관리자 확인
    if user.id not in mode_managers:
        mode_manager = ModeManager(user.id, BybitV5Client)
        mode_managers[user.id] = mode_manager
    else:
        mode_manager = mode_managers[user.id]
    
    settings = mode_manager.get_settings()
    
    # API 키 확인
    if (settings['is_demo'] and not settings['has_demo_api']) or (not settings['is_demo'] and not settings['has_real_api']):
        return jsonify({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email
            },
            'message': f"{'데모' if settings['is_demo'] else '리얼'} 모드 API 키가 설정되지 않았습니다"
        }), 200
    
    # API 클라이언트 초기화
    try:
        api_client = mode_manager.initialize_client()
        if not api_client:
            return jsonify({
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                },
                'error': 'API 연결에 실패했습니다'
            }), 200
        
        # 계정 정보 조회
        wallet = api_client.get_wallet_balance()
        usdt_balance = api_client.get_wallet_coin_balance("USDT")
        
        return jsonify({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email
            },
            'wallet': {
                'totalBalance': wallet.get('totalWalletBalance', 0),
                'totalEquity': wallet.get('totalEquity', 0),
                'availableBalance': wallet.get('availableBalance', 0),
                'usdtBalance': usdt_balance
            },
            'mode': mode_manager.get_mode_info()
        }), 200
    except Exception as e:
        logger.error(f"Error getting account info: {e}")
        return jsonify({
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email
            },
            'error': f'계정 정보 조회 중 오류 발생: {str(e)}'
        }), 200

@app.route('/api/logs', methods=['GET'])
@jwt_required()
def get_logs():
    """로그 조회"""
    username = get_jwt_identity()
    user = User.query.filter_by(username=username).first()
    
    if not user:
        return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404
    
    # 쿼리 파라미터
    limit = request.args.get('limit', 50, type=int)
    if limit > 200:
        limit = 200  # 최대 200개로 제한
    
    log_type = request.args.get('type')
    level = request.args.get('level')
    
    # 로그 조회
    query = Log.query.filter_by(user_id=user.id)
    
    if log_type:
        query = query.filter_by(type=log_type)
    
    if level:
        query = query.filter_by(level=level)
    
    logs = query.order_by(Log.created_at.desc()).limit(limit).all()
    
    result = []
    for log in logs:
        result.append({
            'id': log.id,
            'type': log.type,
            'message': log.message,
            'level': log.level,
            'created_at': log.created_at.isoformat(),
            'metadata': log.get_metadata()
        })
    
    return jsonify({
        'logs': result,
        'count': len(result)
    }), 200

@app.route('/api/realtime-logs', methods=['GET'])
def get_realtime_logs():
    """🟢 시스템/매매 로그 파일 읽기 (system_trade_YYYYMMDD.log)"""
    try:
        # 🟢 현재 날짜 기준 로그 파일 경로
        from datetime import datetime
        current_date = datetime.now().strftime('%Y%m%d')
        log_file_path = os.path.join(os.path.dirname(__file__), 'logs', f'system_trade_{current_date}.log')

        # 파일 존재 확인
        if not os.path.exists(log_file_path):
            return jsonify({
                'success': False,
                'error': f'로그 파일을 찾을 수 없습니다: {log_file_path}',
                'logs': []
            }), 404

        # 마지막 N개 라인 읽기
        limit = request.args.get('limit', 50, type=int)
        if limit > 200:
            limit = 200

        logs = []
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 마지막 N개 라인 가져오기
            recent_lines = lines[-limit:] if len(lines) > limit else lines

            for i, line in enumerate(recent_lines):
                line = line.strip()
                if line and not line.startswith('==='):
                    # 🟢 시간과 메시지 분리 (system_trade_YYYYMMDD.log 형식: "HH:MM:SS  [TYPE] message")
                    parts = line.split('  ', 1)
                    if len(parts) == 2:
                        time_str, full_message = parts

                        # 🟢 [TYPE] 추출
                        log_type = 'system'  # 기본값
                        message = full_message

                        if full_message.startswith('[') and ']' in full_message:
                            bracket_end = full_message.find(']')
                            if bracket_end > 0:
                                type_part = full_message[1:bracket_end].upper()
                                message = full_message[bracket_end + 1:].strip()

                                # 타입 매핑
                                if type_part == 'SYSTEM':
                                    log_type = 'system'
                                elif type_part == 'TRADE':
                                    log_type = 'trade'
                                elif type_part == 'HEDGE':
                                    log_type = 'system'  # 헷징도 시스템으로 분류
                                elif type_part == 'MONITOR':
                                    log_type = 'system'
                                elif type_part == 'ERROR':
                                    log_type = 'error'
                                elif type_part == 'WARNING':
                                    log_type = 'system'
                                else:
                                    log_type = 'system'

                        logs.append({
                            'id': len(recent_lines) - limit + i + 1,
                            'time': time_str,
                            'message': message,
                            'type': log_type,
                            'timestamp': datetime.now().isoformat()
                        })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'로그 파일 읽기 오류: {str(e)}',
                'logs': []
            }), 500

        return jsonify({
            'success': True,
            'logs': logs,
            'count': len(logs),
            'file_path': log_file_path
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'실시간 로그 조회 오류: {str(e)}',
            'logs': []
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """서버 상태 확인"""
    return jsonify({
        'status': 'ok',
        'server_time': datetime.now().isoformat(),
        'version': '1.0.0'
    }), 200

@app.route('/api/realtime-signals', methods=['GET'])
def get_realtime_signals():
    """SignalR에서 실시간 신호 데이터 가져오기"""
    try:
        # SignalR 클라이언트에서 최신 신호들 가져오기
        from api_client.signalr_client import SignalRClient

        # 🟢 로그 전용 SignalR 클라이언트 연결 (매매 실행 없음)
        if not hasattr(app, 'signalr_client'):
            logger.info("로그 전용 SignalR 클라이언트 초기화 시작...")
            from api_client.signalr_client import SignalRClient
            app.signalr_client = SignalRClient(log_only=True)  # 🛡️ 로그 전용 모드
            logger.info("로그 전용 SignalRClient 객체 생성 완료")
            try:
                logger.info("로그 전용 SignalR 클라이언트 start() 호출 중...")
                result = app.signalr_client.start()
                logger.info(f"로그 전용 SignalR start() 결과: {result}")
                logger.info("로그 전용 SignalR 클라이언트가 성공적으로 시작되었습니다 (매매 실행 없음)")
            except Exception as e:
                logger.error(f"로그 전용 SignalR 클라이언트 시작 실패: {e}")
                logger.error(f"예외 타입: {type(e).__name__}")
                logger.error(f"예외 상세: {str(e)}")
                import traceback
                logger.error(f"스택 트레이스: {traceback.format_exc()}")
                app.signalr_client = None

        # SignalR에서 실제 신호 데이터 가져오기
        logs = []
        if app.signalr_client and hasattr(app.signalr_client, 'get_latest_signals'):
            try:
                signals = app.signalr_client.get_latest_signals(max_signals=50)

                # 로그 형태로 변환
                for signal in signals:
                    trading_signal = signal.get('trading_signal', {})
                    signal_type = trading_signal.get('signal_type', 'unknown')

                    # 신호 타입에 따른 메시지 생성
                    if signal_type == 'long':
                        message = f"🟢 롱 신호 감지: {trading_signal.get('description', '롱 포지션 신호')}"
                        log_type = 'signal'
                    elif signal_type == 'short':
                        message = f"🔴 숏 신호 감지: {trading_signal.get('description', '숏 포지션 신호')}"
                        log_type = 'signal'
                    else:
                        message = f"📊 신호 수신: {trading_signal.get('description', '신호 데이터')}"
                        log_type = 'signal'

                    log_entry = {
                        'id': signal.get('processed_at', datetime.now().timestamp()),
                        'type': log_type,
                        'time': datetime.now().strftime('%H:%M:%S'),
                        'message': message,
                        'timestamp': datetime.now().timestamp(),
                        'signal_data': signal  # 원본 신호 데이터 보존
                    }
                    logs.append(log_entry)

            except Exception as e:
                logger.error(f"SignalR 신호 가져오기 실패: {e}")
                # SignalR 실패 시 시스템 로그 추가
                logs.append({
                    'id': datetime.now().timestamp(),
                    'type': 'error',
                    'time': datetime.now().strftime('%H:%M:%S'),
                    'message': f'SignalR 연결 오류: {str(e)}',
                    'timestamp': datetime.now().timestamp(),
                })
        else:
            # SignalR 클라이언트가 없을 때 상태 로그
            logs.append({
                'id': datetime.now().timestamp(),
                'type': 'system',
                'time': datetime.now().strftime('%H:%M:%S'),
                'message': '⚙️ SignalR 클라이언트 초기화 중...',
                'timestamp': datetime.now().timestamp(),
            })

        # SignalR 연결 상태 확인
        connection_status = {'is_connected': False, 'url': 'http://localhost:5000/signalr'}
        if app.signalr_client and hasattr(app.signalr_client, 'get_connection_status'):
            try:
                connection_status = app.signalr_client.get_connection_status()
            except Exception as e:
                logger.error(f"SignalR 연결 상태 확인 실패: {e}")

        return jsonify({
            'success': True,
            'logs': logs,
            'connection_status': connection_status
        }), 200

    except Exception as e:
        logger.error(f"SignalR 데이터 가져오기 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'logs': []
        }), 500

@app.route('/api/simple-rankings', methods=['GET'])
def get_simple_rankings():
    """간단한 사용자 순위 (인증 없음)"""
    try:
        import random

        # 모든 활성 사용자 조회
        users = User.query.filter_by(is_active=True).all()
        rankings = []

        for user in users:
            # 간단한 통계 계산
            total_trades = Trade.query.filter_by(user_id=user.id).count()

            # 임시 수익률 (실제로는 계산 로직 필요)
            profit_percentage = random.uniform(-50, 300)  # 임시값

            rankings.append({
                'username': user.username,
                'profit_percentage': round(profit_percentage, 1),
                'total_trades': total_trades,
            })

        # 수익률 기준 정렬
        rankings.sort(key=lambda x: x['profit_percentage'], reverse=True)

        # 순위 및 메달 추가
        for i, ranking in enumerate(rankings):
            ranking['rank'] = i + 1
            if i == 0:
                ranking['medal'] = '🥇'
            elif i == 1:
                ranking['medal'] = '🥈'
            elif i == 2:
                ranking['medal'] = '🥉'

        return jsonify({'rankings': rankings}), 200

    except Exception as e:
        logger.error(f"순위 조회 오류: {e}")
        return jsonify({'error': str(e)}), 500

@app.teardown_appcontext
def shutdown_session(exception=None):
    """애플리케이션 컨텍스트 종료 시 세션 정리"""
    db_session.remove()

# 애플리케이션 시작 지점
if __name__ == '__main__':
    # 포트 3000으로 설정 (포트 충돌 방지)
    port = int(os.environ.get('PORT', 3000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    logger.info(f"Starting Python Backend on port {port}, debug mode: {debug}")
    app.run(host='0.0.0.0', port=port, debug=debug)