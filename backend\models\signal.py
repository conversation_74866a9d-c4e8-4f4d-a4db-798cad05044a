# 신호 모델
# models/signal.py
import datetime
import json
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from database import Base
from utils.helpers import utc_now

class Signal(Base):
    """신호 모델"""
    __tablename__ = 'signals'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    symbol = Column(String(20), nullable=False)
    data = Column(Text, nullable=False)  # JSON 형식 신호 데이터
    received_at = Column(DateTime, default=datetime.datetime.utcnow)
    is_processed = Column(Boolean, default=False)
    result = Column(Text)  # JSON 형식 처리 결과
    processed_at = Column(DateTime)
    
    # 관계 설정
    user = relationship("User", back_populates="signals")
    
    def __init__(self, user_id, symbol, data, received_at=None, is_processed=False):
        """
        신호 초기화
        
        Args:
            user_id: 사용자 ID
            symbol: 거래 심볼
            data: 신호 데이터 (JSON 형식 문자열 또는 딕셔너리)
            received_at: 수신 시간
            is_processed: 처리 여부
        """
        self.user_id = user_id
        self.symbol = symbol
        
        # 데이터 처리
        if isinstance(data, dict):
            self.data = json.dumps(data)
        else:
            self.data = data
        
        self.received_at = received_at or utc_now()
        self.is_processed = is_processed
    
    def set_result(self, result):
        """결과 설정"""
        if isinstance(result, dict):
            self.result = json.dumps(result)
        else:
            self.result = result
        
        self.is_processed = True
        self.processed_at = utc_now()
    
    def get_data(self):
        """신호 데이터 조회"""
        if not self.data:
            return {}
        
        try:
            return json.loads(self.data)
        except json.JSONDecodeError:
            return {}
    
    def get_result(self):
        """처리 결과 조회"""
        if not self.result:
            return {}
        
        try:
            return json.loads(self.result)
        except json.JSONDecodeError:
            return {}
    
    def get_colors(self):
        """신호 색상 목록 조회"""
        data = self.get_data()
        return data.get('colors', [])
    
    def to_dict(self):
        """
        신호 정보를 딕셔너리로 변환
        
        Returns:
            신호 정보 딕셔너리
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'symbol': self.symbol,
            'data': self.get_data(),
            'colors': self.get_colors(),
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'is_processed': self.is_processed,
            'result': self.get_result(),
            'processed_at': self.processed_at.isoformat() if self.processed_at else None
        }
    
    def __repr__(self):
        return f"<Signal(id={self.id}, user_id={self.user_id}, symbol='{self.symbol}', is_processed={self.is_processed})>"