# 관리자 API 라우트
# routes/admin_routes.py
import logging
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from functools import wraps

from database import db_session
from models.user import User
from models.log import Log
from models.trade import Trade
from models.position import Position
from models.signal import Signal

logger = logging.getLogger("opensystems_bot")

admin_bp = Blueprint('admin', __name__)

def admin_required(level=1):
    """어드민 권한 확인 데코레이터"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            username = get_jwt_identity()
            user = User.query.filter_by(username=username).first()

            if not user:
                return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

            if not user.is_admin or user.admin_level < level:
                return jsonify({'error': '관리자 권한이 필요합니다'}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

@admin_bp.route('/dashboard', methods=['GET'])
@admin_required(level=1)
def get_dashboard_stats():
    """어드민 대시보드 통계"""
    try:
        # 사용자 통계
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        admin_users = User.query.filter_by(is_admin=True).count()

        # 거래 통계
        total_trades = Trade.query.count()
        today_trades = Trade.query.filter(
            Trade.executed_at >= datetime.utcnow().date()
        ).count()

        # 포지션 통계
        active_positions = Position.query.filter_by(status='ACTIVE').count()

        # 신호 통계
        total_signals = Signal.query.count()
        today_signals = Signal.query.filter(
            Signal.received_at >= datetime.utcnow().date()
        ).count()
        processed_signals = Signal.query.filter_by(is_processed=True).count()

        return jsonify({
            'users': {
                'total': total_users,
                'active': active_users,
                'admin': admin_users
            },
            'trades': {
                'total': total_trades,
                'today': today_trades
            },
            'positions': {
                'active': active_positions
            },
            'signals': {
                'total': total_signals,
                'today': today_signals,
                'processed': processed_signals,
                'processing_rate': round((processed_signals / total_signals * 100), 2) if total_signals > 0 else 0
            },
            'server_time': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"대시보드 통계 조회 오류: {e}")
        return jsonify({'error': '통계 조회에 실패했습니다'}), 500

@admin_bp.route('/users', methods=['GET'])
@admin_required(level=1)
def get_all_users():
    """모든 사용자 목록 조회"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        users = User.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        user_list = []
        for user in users.items:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'admin_level': user.admin_level,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
            user_list.append(user_data)

        return jsonify({
            'users': user_list,
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total
            }
        }), 200

    except Exception as e:
        logger.error(f"사용자 목록 조회 오류: {e}")
        return jsonify({'error': '사용자 목록 조회에 실패했습니다'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@admin_required(level=2)
def delete_user(user_id):
    """사용자 삭제 (비활성화)"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        # 관리자는 삭제할 수 없음
        if user.is_admin:
            return jsonify({'error': '관리자 계정은 삭제할 수 없습니다'}), 400

        # 사용자 비활성화
        user.is_active = False
        db_session.commit()

        return jsonify({'message': '사용자가 비활성화되었습니다'}), 200

    except Exception as e:
        logger.error(f"사용자 삭제 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '사용자 삭제에 실패했습니다'}), 500

@admin_bp.route('/users', methods=['POST'])
@admin_required(level=2)
def create_user():
    """사용자 생성"""
    try:
        data = request.get_json()

        if not data or not data.get('username') or not data.get('email') or not data.get('password'):
            return jsonify({'error': '필수 필드가 누락되었습니다'}), 400

        # 중복 확인
        existing_user = User.query.filter(
            (User.username == data['username']) | (User.email == data['email'])
        ).first()

        if existing_user:
            return jsonify({'error': '이미 존재하는 사용자명 또는 이메일입니다'}), 400

        # 새 사용자 생성
        new_user = User(
            username=data['username'],
            email=data['email'],
            password=data['password'],
            is_admin=data.get('is_admin', False),
            admin_level=data.get('admin_level', 0)
        )

        db_session.add(new_user)
        db_session.commit()

        return jsonify({
            'message': '사용자가 생성되었습니다',
            'user': {
                'id': new_user.id,
                'username': new_user.username,
                'email': new_user.email,
                'is_admin': new_user.is_admin,
                'admin_level': new_user.admin_level
            }
        }), 201

    except Exception as e:
        logger.error(f"사용자 생성 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '사용자 생성에 실패했습니다'}), 500
# routes/admin_routes.py
import logging
from datetime import datetime, timedelta
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from functools import wraps

from database import db_session
from models.user import User
from models.log import Log
from models.trade import Trade
from models.position import Position
from models.signal import Signal
from utils.helpers import generate_id

logger = logging.getLogger("opensystems_bot")

admin_bp = Blueprint('admin', __name__)

def admin_required(level=1):
    """어드민 권한 확인 데코레이터"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            username = get_jwt_identity()
            user = User.query.filter_by(username=username).first()

            if not user:
                return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

            if not user.is_admin or user.admin_level < level:
                return jsonify({'error': '관리자 권한이 필요합니다'}), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

@admin_bp.route('/users', methods=['GET'])
@admin_required(level=1)
def get_all_users():
    """모든 사용자 목록 조회"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        users = User.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        user_list = []
        for user in users.items:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'admin_level': user.admin_level,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
            user_list.append(user_data)

        return jsonify({
            'users': user_list,
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total
            }
        }), 200

    except Exception as e:
        logger.error(f"사용자 목록 조회 오류: {e}")
        return jsonify({'error': '사용자 목록 조회에 실패했습니다'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['DELETE'])
@admin_required(level=2)
def delete_user(user_id):
    """사용자 삭제"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        # 관리자는 삭제할 수 없음
        if user.is_admin:
            return jsonify({'error': '관리자 계정은 삭제할 수 없습니다'}), 400

        # 사용자 비활성화 (실제 삭제 대신)
        user.is_active = False
        db_session.commit()

        # 로그 기록
        log = Log(
            user_id=get_jwt_identity(),
            action='USER_DELETE',
            details=f'사용자 삭제: {user.username}',
            timestamp=datetime.utcnow()
        )
        db_session.add(log)
        db_session.commit()

        return jsonify({'message': '사용자가 비활성화되었습니다'}), 200

    except Exception as e:
        logger.error(f"사용자 삭제 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '사용자 삭제에 실패했습니다'}), 500

@admin_bp.route('/users', methods=['POST'])
@admin_required(level=2)
def create_user():
    """사용자 생성"""
    try:
        data = request.get_json()

        if not data or not data.get('username') or not data.get('email') or not data.get('password'):
            return jsonify({'error': '필수 필드가 누락되었습니다'}), 400

        # 중복 확인
        existing_user = User.query.filter(
            (User.username == data['username']) | (User.email == data['email'])
        ).first()

        if existing_user:
            return jsonify({'error': '이미 존재하는 사용자명 또는 이메일입니다'}), 400

        # 새 사용자 생성
        new_user = User(
            username=data['username'],
            email=data['email'],
            password=data['password'],  # User 모델에서 자동 해싱
            is_admin=data.get('is_admin', False),
            admin_level=data.get('admin_level', 0)
        )

        db_session.add(new_user)
        db_session.commit()

        # 로그 기록
        log = Log(
            user_id=get_jwt_identity(),
            action='USER_CREATE',
            details=f'사용자 생성: {new_user.username}',
            timestamp=datetime.utcnow()
        )
        db_session.add(log)
        db_session.commit()

        return jsonify({
            'message': '사용자가 생성되었습니다',
            'user': {
                'id': new_user.id,
                'username': new_user.username,
                'email': new_user.email,
                'is_admin': new_user.is_admin,
                'admin_level': new_user.admin_level
            }
        }), 201

    except Exception as e:
        logger.error(f"사용자 생성 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '사용자 생성에 실패했습니다'}), 500

@admin_bp.route('/users/<int:user_id>', methods=['PUT'])
@admin_required(level=2)
def update_user(user_id):
    """사용자 정보 업데이트"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 데이터가 없습니다'}), 400

        # 업데이트 가능한 필드들
        if 'email' in data:
            user.email = data['email']
        if 'is_active' in data:
            user.is_active = data['is_active']
        if 'is_admin' in data:
            user.is_admin = data['is_admin']
        if 'admin_level' in data:
            user.admin_level = data['admin_level']

        user.updated_at = datetime.utcnow()
        db_session.commit()

        # 로그 기록
        log = Log(
            user_id=get_jwt_identity(),
            action='USER_UPDATE',
            details=f'사용자 업데이트: {user.username}',
            timestamp=datetime.utcnow()
        )
        db_session.add(log)
        db_session.commit()

        return jsonify({
            'message': '사용자 정보가 업데이트되었습니다',
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active,
                'is_admin': user.is_admin,
                'admin_level': user.admin_level
            }
        }), 200

    except Exception as e:
        logger.error(f"사용자 업데이트 오류: {e}")
        db_session.rollback()
        return jsonify({'error': '사용자 업데이트에 실패했습니다'}), 500
