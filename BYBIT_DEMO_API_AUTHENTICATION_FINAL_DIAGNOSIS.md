# Bybit Demo API 인증 오류 최종 진단 보고서

## 현재 상황 요약
- **오류 코드**: 10003 (API key is invalid)
- **영향**: 모든 계정/거래 관련 API 호출 실패
- **작동**: 공개 API 엔드포인트만 정상 작동 (시세, 심볼, 서버 시간)
- **환경**: Demo 모드 (`https://api-demo.bybit.com`)

## 공식 Bybit V5 Demo API 문서 분석

### 1. Demo API 키 생성 요구사항 (공식 문서 기준)

**정확한 생성 절차:**
1. **메인넷 계정** (`https://www.bybit.com/`)에 로그인
2. **"Demo Trading"으로 전환** (독립된 데모 계정, 별도 사용자 ID)
3. 사용자 아바타 hover → **"API" 클릭**하여 API 키와 시크릿 생성

**중요 사항:**
- Demo Trading은 **격리된 모듈**
- Demo 키는 **반드시** `https://api-demo.bybit.com` 도메인과 함께 사용
- Testnet에서 Demo 키 생성은 **무의미함** (공식 경고)

### 2. 현재 설정 검증

**현재 사용 중인 설정:**
```
BYBIT_API_KEY=DEMO_TEST_KEY_123
BYBIT_API_SECRET=DEMO_TEST_SECRET_456
BYBIT_BASE_URL=https://api-demo.bybit.com
```

**문제점 식별:**
- API 키가 `DEMO_TEST_KEY_123` 형태로 **테스트용 더미 값**으로 보임
- 실제 Bybit에서 발급된 공식 Demo API 키가 아닐 가능성 높음

### 3. 10003 오류 발생 원인 분석

**주요 원인 (우선순위별):**

1. **잘못된 API 키 사용 (가장 가능성 높음)**
   - 현재 키가 실제 Bybit Demo API 키가 아님
   - 테스트용 더미 값 사용

2. **잘못된 키 생성 위치**
   - Testnet에서 생성된 키 사용
   - 메인넷 일반 계정에서 생성된 키 사용 (Demo Trading 아님)

3. **키 권한 문제**
   - Demo Trading 권한이 없는 키
   - 만료되었거나 비활성화된 키

4. **IP 제한**
   - Demo API 키에 IP 화이트리스트 설정됨

### 4. 봇 기능에 미치는 영향

**차단된 기능 (10003 오류로 인해):**
- 모든 거래 실행 (`/v5/order/create`, `/v5/order/cancel` 등)
- 포지션 관리 (`/v5/position/list`, `/v5/position/set-leverage` 등)
- 계정 잔고 조회 (`/v5/account/wallet-balance`)
- 거래 내역 조회 (`/v5/execution/list`)
- 계정 정보 조회 (`/v5/account/info`)

**정상 작동 기능:**
- 시장 데이터 조회 (시세, 심볼 정보, 서버 시간)

## 해결 방안

### 즉시 실행 필요 조치

1. **올바른 Demo API 키 생성**
   ```
   1. https://www.bybit.com/ 로그인
   2. "Demo Trading" 모드로 전환
   3. 사용자 아바타 → "API" → 새 키 생성
   4. 생성된 실제 키/시크릿으로 .env 파일 업데이트
   ```

2. **키 권한 확인**
   - 거래 권한 활성화
   - IP 제한 해제 또는 현재 IP 추가

3. **설정 검증**
   - 도메인: `https://api-demo.bybit.com` 유지
   - 키 형식: 실제 Bybit 형식 (일반적으로 영숫자 조합)

### 검증 방법

새 키 적용 후 다음 명령으로 검증:
```powershell
cd c:\project\OPENSYSTEMS_BOT_V3.1
python test_bybit_v5.py
```

성공 시 계정 잔고 조회가 정상 작동해야 함.

## 추가 고려사항

### Demo 자금 신청
새 키로 인증 성공 후:
```
POST /v5/account/demo-apply-money
- BTC: 최대 15
- ETH: 최대 200  
- USDT: 최대 100,000
- USDC: 최대 100,000
```

### 제한사항
- Demo 주문은 7일간 보존
- 기본 레이트 제한 (업그레이드 불가)
- 일부 API 기능 제한 (거래 체험 목적)

## 결론

현재 10003 오류는 **유효하지 않은 API 키 사용**이 주요 원인입니다. 현재 사용 중인 `DEMO_TEST_KEY_123`는 실제 Bybit에서 발급된 키가 아닌 테스트용 더미 값으로 보입니다.

**즉시 필요한 조치:** Bybit 메인넷 계정에서 Demo Trading 모드로 전환하여 실제 Demo API 키를 생성하고 적용해야 합니다.

이 조치 완료 후 모든 거래 및 계정 관리 기능이 정상 작동할 것으로 예상됩니다.
