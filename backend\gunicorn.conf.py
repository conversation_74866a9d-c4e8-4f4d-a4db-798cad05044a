# gunicorn.conf.py - Unicorn 스타일 설정
"""
Gunicorn 설정 파일 (Unicorn 철학 기반)
트레이딩 봇 시스템 최적화 설정
"""

import multiprocessing
import os

# 서버 소켓 (Unicorn 스타일)
bind = "0.0.0.0:8080"
backlog = 1024  # Unicorn 기본값

# 워커 프로세스 (Unicorn 철학: CPU 코어 수와 동일)
workers = multiprocessing.cpu_count()
worker_class = "sync"  # Unicorn과 동일한 동기 모델
worker_connections = 1000

# Unicorn 스타일 메모리 관리
max_requests = 1000  # 워커 재시작으로 메모리 누수 방지
max_requests_jitter = 100  # 재시작 시점 분산

# 타임아웃 (Unicorn 스타일)
timeout = 60  # 트레이딩 API 호출 고려
keepalive = 5  # 연결 유지 시간 증가

# 로깅 (Unicorn 스타일)
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 프로세스 이름 (Unicorn 스타일)
proc_name = "opensystems_bot_master"

# 데몬 모드 (Unicorn 기본값: False)
daemon = False

# PID 파일 (Unicorn 스타일)
pidfile = "logs/gunicorn.pid"

# 사용자/그룹 (Linux에서만)
# user = "www-data"
# group = "www-data"

# 프리로드 (Unicorn 핵심 기능)
preload_app = True  # 마스터 프로세스에서 앱 로드 후 워커에 포크

# 그레이스풀 타임아웃 (Unicorn 스타일)
graceful_timeout = 30  # 워커 종료 대기 시간

# Unicorn 스타일 시그널 처리
# QUIT: 그레이스풀 셧다운
# TERM: 빠른 셧다운
# USR2: 새 마스터 프로세스 시작 (무중단 재시작)

# 환경 변수
raw_env = [
    'FLASK_ENV=production',
    'PYTHONPATH=/app',
]

# Unicorn 스타일 워커 관리
worker_tmp_dir = "/dev/shm"  # 메모리 기반 임시 디렉토리 (Linux)
tmp_upload_dir = None
