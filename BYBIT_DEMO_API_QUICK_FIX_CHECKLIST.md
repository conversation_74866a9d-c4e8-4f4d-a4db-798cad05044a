# Bybit Demo API 인증 문제 해결 체크리스트

## 🚀 빠른 해결 가이드 (5분 완료)

### ✅ 1단계: 환경 변수 확인
```bash
# .env 파일 위치 확인
ls backend/.env

# 환경 변수 내용 확인 (키 값 숨김)
python -c "
import os
from dotenv import load_dotenv
load_dotenv('backend/.env')
print('✅ API Key:', os.getenv('BYBIT_API_KEY', 'NOT_SET')[:10] + '...' if os.getenv('BYBIT_API_KEY') else '❌ NOT_SET')
print('✅ Secret:', '*' * 20 if os.getenv('BYBIT_SECRET_KEY') else '❌ NOT_SET')  
print('✅ URL:', os.getenv('BASE_URL', 'NOT_SET'))
"
```

### ✅ 2단계: API 헤더 형식 확인
```python
# ❌ 잘못된 V4 형식
headers = {
    "X-BYBIT-API-KEY": api_key,
    "X-BYBIT-SIGN": signature,
    "X-BYBIT-TS": timestamp,
    "X-BYBIT-RECV-WINDOW": recv_window
}

# ✅ 올바른 V5 형식  
headers = {
    "X-BAPI-API-KEY": api_key,
    "X-BAPI-SIGN": signature,
    "X-BAPI-TIMESTAMP": timestamp,
    "X-BAPI-RECV-WINDOW": recv_window
}
```

### ✅ 3단계: 즉시 테스트
```bash
# Demo API 테스트 실행
python demo_check_balance.py

# 성공 시 출력 예시:
# ✅ Success! Wallet Balance:
# Account Type: UNIFIED
# Coins: USDC: Wallet=50000, BTC: Wallet=1, ETH: Wallet=1, USDT: Wallet=48991.77
```

---

## 🔧 상세 기술 참조

### API 키 생성 방법
1. https://www.bybit.com 로그인
2. "Demo Trading" 모드 전환  
3. 사용자 아바타 → "API" 클릭
4. 새 API 키 생성 (거래 권한 포함)

### 서명 생성 공식
```python
param_str = timestamp + api_key + recv_window + query_string
signature = hmac.new(api_secret.encode(), param_str.encode(), hashlib.sha256).hexdigest()
```

### 일반적인 오류 코드
- `10003`: API key is invalid (헤더 형식 문제)
- `10004`: Invalid API key permissions  
- `10005`: Permission denied
- `401`: Unauthorized (서명 오류)

---

## 📞 문제 해결 연락처

### 공식 지원 채널
- 📚 문서: https://bybit-exchange.github.io/docs/v5/demo
- 💬 텔레그램: https://t.me/BybitAPI  
- 💬 디스코드: https://discord.gg/VBwVwS2HUs
- 📧 깃허브: https://github.com/bybit-exchange/api-usage-examples

### 추가 자료
- 🐍 Python SDK: https://github.com/bybit-exchange/pybit
- 📝 예제 코드: https://github.com/bybit-exchange/api-usage-examples
- 🔧 Postman 컬렉션: https://github.com/bybit-exchange/QuickStartWithPostman

---

**업데이트**: 2025년 6월 20일  
**상태**: ✅ 검증 완료
