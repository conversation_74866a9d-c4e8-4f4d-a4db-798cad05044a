# Bybit V5 Demo API 클라이언트 - 표준 구현

"""
완전히 검증된 Bybit V5 Demo API 클라이언트
- 올바른 헤더 형식 (X-BAPI-*)
- 정확한 서명 생성
- 포괄적인 에러 처리
- Demo 모드 최적화
"""

import requests
import time
import hmac
import hashlib
import os
import urllib.parse
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class BybitV5DemoClient:
    """Bybit V5 Demo API 클라이언트"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, base_url: str = None):
        # 환경 변수에서 로드
        load_dotenv('backend/.env')
        
        self.api_key = api_key or os.getenv('BYBIT_API_KEY')
        self.api_secret = api_secret or os.getenv('BYBIT_SECRET_KEY')
        self.base_url = base_url or os.getenv('BASE_URL', 'https://api-demo.bybit.com')
        
        if not self.api_key or not self.api_secret:
            raise ValueError("API key and secret are required")
    
    def _generate_signature(self, timestamp: str, recv_window: str, query_string: str) -> str:
        """Bybit V5 API 서명 생성"""
        param_str = f"{timestamp}{self.api_key}{recv_window}{query_string}"
        
        signature = hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"), 
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None) -> Dict[str, Any]:
        """API 요청 생성 및 전송"""
        timestamp = str(int(time.time() * 1000))
        recv_window = "5000"
        
        # 쿼리 스트링 생성
        if params:
            query_string = urllib.parse.urlencode(params, safe='')
        else:
            query_string = ""
        
        # 서명 생성
        signature = self._generate_signature(timestamp, recv_window, query_string)
        
        # Bybit V5 헤더 (중요: X-BAPI- 형식)
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": recv_window,
            "Content-Type": "application/json"
        }
        
        # URL 구성
        url = f"{self.base_url}{endpoint}"
        if query_string:
            url += f"?{query_string}"
        
        # 요청 전송
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # 응답 파싱
            data = response.json()
            
            # 에러 처리
            if response.status_code != 200:
                raise APIError(f"HTTP {response.status_code}: {data}")
            
            ret_code = data.get('retCode', -1)
            if ret_code != 0:
                ret_msg = data.get('retMsg', 'Unknown error')
                raise APIError(f"API Error {ret_code}: {ret_msg}")
            
            return data.get('result', {})
            
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Request failed: {e}")
        except ValueError as e:
            raise APIError(f"Invalid response format: {e}")
    
    # ======================
    # 계정 관련 API
    # ======================
    
    def get_account_info(self) -> Dict[str, Any]:
        """계정 정보 조회"""
        return self._make_request('GET', '/v5/account/info')
    
    def get_wallet_balance(self, account_type: str = 'UNIFIED') -> Dict[str, Any]:
        """지갑 잔고 조회"""
        params = {'accountType': account_type}
        return self._make_request('GET', '/v5/account/wallet-balance', params)
    
    def apply_demo_funds(self, coins_amounts: list) -> Dict[str, Any]:
        """Demo 자금 신청
        
        Args:
            coins_amounts: [{'coin': 'USDT', 'amountStr': '1000'}, ...]
        """
        params = {
            'adjustType': 0,  # 0: add funds, 1: reduce funds
            'utaDemoApplyMoney': coins_amounts
        }
        return self._make_request('POST', '/v5/account/demo-apply-money', params)
    
    # ======================
    # 포지션 관련 API  
    # ======================
    
    def get_positions(self, category: str = 'linear', symbol: str = None) -> Dict[str, Any]:
        """포지션 정보 조회"""
        params = {'category': category}
        if symbol:
            params['symbol'] = symbol
        return self._make_request('GET', '/v5/position/list', params)
    
    def get_closed_pnl(self, category: str = 'linear', limit: int = 50) -> Dict[str, Any]:
        """마감된 손익 조회"""
        params = {
            'category': category,
            'limit': limit
        }
        return self._make_request('GET', '/v5/position/closed-pnl', params)
    
    # ======================
    # 거래 관련 API
    # ======================
    
    def place_order(self, category: str, symbol: str, side: str, order_type: str, 
                   qty: str, price: str = None, **kwargs) -> Dict[str, Any]:
        """주문 생성"""
        params = {
            'category': category,
            'symbol': symbol,
            'side': side,
            'orderType': order_type,
            'qty': qty
        }
        
        if price:
            params['price'] = price
        
        # 추가 파라미터
        params.update(kwargs)
        
        return self._make_request('POST', '/v5/order/create', params)
    
    def cancel_order(self, category: str, symbol: str, order_id: str = None, 
                    order_link_id: str = None) -> Dict[str, Any]:
        """주문 취소"""
        params = {
            'category': category,
            'symbol': symbol
        }
        
        if order_id:
            params['orderId'] = order_id
        elif order_link_id:
            params['orderLinkId'] = order_link_id
        else:
            raise ValueError("Either order_id or order_link_id is required")
        
        return self._make_request('POST', '/v5/order/cancel', params)
    
    def get_open_orders(self, category: str, symbol: str = None) -> Dict[str, Any]:
        """활성 주문 조회"""
        params = {'category': category}
        if symbol:
            params['symbol'] = symbol
        return self._make_request('GET', '/v5/order/realtime', params)
    
    def get_order_history(self, category: str, symbol: str = None, limit: int = 50) -> Dict[str, Any]:
        """주문 내역 조회"""
        params = {
            'category': category,
            'limit': limit
        }
        if symbol:
            params['symbol'] = symbol
        return self._make_request('GET', '/v5/order/history', params)
    
    # ======================
    # 시장 데이터 API (인증 불필요)
    # ======================
    
    def get_server_time(self) -> Dict[str, Any]:
        """서버 시간 조회"""
        url = f"{self.base_url}/v5/market/time"
        response = requests.get(url)
        return response.json().get('result', {})
    
    def get_ticker(self, category: str, symbol: str) -> Dict[str, Any]:
        """시세 정보 조회"""
        url = f"{self.base_url}/v5/market/tickers"
        params = {'category': category, 'symbol': symbol}
        response = requests.get(url, params=params)
        result = response.json().get('result', {})
        tickers = result.get('list', [])
        return tickers[0] if tickers else {}
    
    def get_symbols(self, category: str) -> list:
        """거래 가능한 심볼 조회"""
        url = f"{self.base_url}/v5/market/instruments-info"
        params = {'category': category}
        response = requests.get(url, params=params)
        result = response.json().get('result', {})
        return result.get('list', [])


# ======================
# 예외 클래스
# ======================

class APIError(Exception):
    """API 오류"""
    pass


class ConnectionError(Exception):
    """연결 오류"""
    pass


# ======================
# 사용 예시
# ======================

def demo_usage():
    """사용 예시"""
    try:
        # 클라이언트 생성
        client = BybitV5DemoClient()
        
        # 서버 시간 확인
        server_time = client.get_server_time()
        print(f"✅ 서버 시간: {server_time}")
        
        # 계정 정보 조회
        account_info = client.get_account_info()
        print(f"✅ 계정 정보: {account_info}")
        
        # 지갑 잔고 조회
        wallet = client.get_wallet_balance()
        print(f"✅ 지갑 잔고: {wallet}")
        
        # 포지션 조회
        positions = client.get_positions()
        print(f"✅ 포지션: {positions}")
        
        # 시세 조회 (BTCUSDT)
        ticker = client.get_ticker('linear', 'BTCUSDT')
        print(f"✅ BTC 시세: {ticker}")
        
        print("\n🎉 모든 API 호출 성공!")
        
    except APIError as e:
        print(f"❌ API 오류: {e}")
    except ConnectionError as e:
        print(f"❌ 연결 오류: {e}")
    except Exception as e:
        print(f"❌ 예상치 못한 오류: {e}")


if __name__ == "__main__":
    demo_usage()
