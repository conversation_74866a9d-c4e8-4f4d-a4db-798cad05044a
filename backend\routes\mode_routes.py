# 모드 관리 API 라우트
# routes/mode_routes.py
import logging
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity

from database import db_session
from models.user import User
from api_client.bybit_v5_client import BybitV5Client
from bot_core.mode_manager import ModeManager

logger = logging.getLogger("opensystems_bot")

mode_bp = Blueprint('mode', __name__)

# 전역 모드 관리자 인스턴스들
mode_managers = {}

def get_mode_manager(user_id):
    """사용자별 모드 관리자 인스턴스 반환"""
    if user_id not in mode_managers:
        mode_managers[user_id] = ModeManager(user_id, BybitV5Client)
    return mode_managers[user_id]

@mode_bp.route('/info', methods=['GET'])
@jwt_required()
def get_mode_info():
    """현재 모드 정보 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        mode_manager = get_mode_manager(user.id)
        mode_info = mode_manager.get_mode_info()

        return jsonify({
            'success': True,
            'mode_info': mode_info
        }), 200

    except Exception as e:
        logger.error(f"모드 정보 조회 오류: {e}")
        return jsonify({'error': '모드 정보 조회 중 오류가 발생했습니다'}), 500

@mode_bp.route('/switch', methods=['POST'])
@jwt_required()
def switch_mode():
    """모드 전환 (데모 ↔ 리얼)"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data or 'is_demo' not in data:
            return jsonify({'error': '모드 정보가 필요합니다'}), 400

        to_demo = data['is_demo']

        mode_manager = get_mode_manager(user.id)
        result = mode_manager.switch_mode(to_demo)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"모드 전환 오류: {e}")
        return jsonify({'error': '모드 전환 중 오류가 발생했습니다'}), 500

@mode_bp.route('/settings', methods=['GET'])
@jwt_required()
def get_settings():
    """설정 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        mode_manager = get_mode_manager(user.id)
        settings = mode_manager.get_settings()

        return jsonify({
            'success': True,
            'settings': settings
        }), 200

    except Exception as e:
        logger.error(f"설정 조회 오류: {e}")
        return jsonify({'error': '설정 조회 중 오류가 발생했습니다'}), 500

@mode_bp.route('/settings', methods=['PUT'])
@jwt_required()
def update_settings():
    """설정 업데이트"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 설정이 없습니다'}), 400

        mode_manager = get_mode_manager(user.id)
        result = mode_manager.update_settings(**data)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"설정 업데이트 오류: {e}")
        return jsonify({'error': '설정 업데이트 중 오류가 발생했습니다'}), 500

@mode_bp.route('/account', methods=['GET'])
@jwt_required()
def get_account_info():
    """계정 정보 조회"""
    try:
        username = get_jwt_identity()
        user = User.query.filter_by(username=username).first()

        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        mode_manager = get_mode_manager(user.id)
        account_info = mode_manager.get_account_info()

        return jsonify({
            'success': True,
            'account_info': account_info
        }), 200

    except Exception as e:
        logger.error(f"계정 정보 조회 오류: {e}")
        return jsonify({'error': '계정 정보 조회 중 오류가 발생했습니다'}), 500

@mode_bp.route('/api-keys', methods=['POST'])
# @jwt_required()  # 임시로 JWT 인증 제거 - 매매 테스트용
def save_api_keys():
    """API 키 저장"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()

        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': 'API 키 정보가 필요합니다'}), 400

        # 필수 필드 확인
        required_fields = ['api_key', 'api_secret', 'is_demo']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field}는 필수 입력 항목입니다'}), 400

        api_key = data['api_key'].strip()
        api_secret = data['api_secret'].strip()
        is_demo = data['is_demo']

        if not api_key or not api_secret:
            return jsonify({'error': 'API 키와 시크릿을 모두 입력해주세요'}), 400

        # 모드에 따라 적절한 필드에 저장
        mode_manager = get_mode_manager(user.id)

        if is_demo:
            result = mode_manager.update_settings(
                demo_api_key=api_key,
                demo_api_secret=api_secret
            )
        else:
            result = mode_manager.update_settings(
                real_api_key=api_key,
                real_api_secret=api_secret
            )

        if result['success']:
            return jsonify({
                'success': True,
                'message': f"{'데모' if is_demo else '리얼'} 모드 API 키가 저장되었습니다"
            }), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"API 키 저장 오류: {e}")
        return jsonify({'error': 'API 키 저장 중 오류가 발생했습니다'}), 500

@mode_bp.route('/settings-no-auth', methods=['PUT'])
# @jwt_required() 제거 - 데모 모드 매매 테스트용
def update_settings_no_auth():
    """설정 업데이트 (인증 없음 - 데모 모드용)"""
    try:
        # SQLite 데이터베이스에서 어드민 사용자 조회
        user = User.query.filter_by(username='<EMAIL>').first()
        if not user:
            return jsonify({'error': '어드민 사용자를 찾을 수 없습니다'}), 404

        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 설정이 없습니다'}), 400

        # 디버깅: 받은 데이터 확인
        logger.info(f"🔑 받은 API 키 데이터: {data}")
        if 'demo_api_key' in data:
            logger.info(f"  demo_api_key: '{data['demo_api_key']}' (길이: {len(data['demo_api_key']) if data['demo_api_key'] else 0})")
        if 'demo_api_secret' in data:
            logger.info(f"  demo_api_secret: '{data['demo_api_secret']}' (길이: {len(data['demo_api_secret']) if data['demo_api_secret'] else 0})")

        mode_manager = get_mode_manager(user.id)
        result = mode_manager.update_settings(**data)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"설정 업데이트 오류 (인증 없음): {e}")
        return jsonify({'error': '설정 업데이트 중 오류가 발생했습니다'}), 500

@mode_bp.route('/settings-with-user', methods=['PUT'])
def update_settings_with_user():
    """설정 업데이트 (사용자명 파라미터 방식)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '업데이트할 설정이 없습니다'}), 400

        username = data.get('username')
        if not username:
            return jsonify({'error': '사용자명이 필요합니다'}), 400

        # 사용자명으로 사용자 조회
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'error': '사용자를 찾을 수 없습니다'}), 404

        # 디버깅: 받은 데이터 확인
        logger.info(f"🔑 받은 API 키 데이터 (사용자: {username}): {data}")
        if 'demo_api_key' in data:
            logger.info(f"  demo_api_key: '{data['demo_api_key']}' (길이: {len(data['demo_api_key']) if data['demo_api_key'] else 0})")
        if 'demo_api_secret' in data:
            logger.info(f"  demo_api_secret: '{data['demo_api_secret']}' (길이: {len(data['demo_api_secret']) if data['demo_api_secret'] else 0})")

        mode_manager = get_mode_manager(user.id)
        result = mode_manager.update_settings(**data)

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"설정 업데이트 오류 (사용자명 방식): {e}")
        return jsonify({'error': '설정 업데이트 중 오류가 발생했습니다'}), 500
