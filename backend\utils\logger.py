# utils/logger.py
import os
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import json
from datetime import datetime
import traceback

# 로그 레벨 매핑
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

def setup_logger(name, log_level=None, log_dir=None, max_size=10*1024*1024, backup_count=5):
    """
    로거 설정
    
    Args:
        name: 로거 이름
        log_level: 로그 레벨 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: 로그 파일 디렉토리
        max_size: 로그 파일 최대 크기 (바이트)
        backup_count: 백업 파일 개수
        
    Returns:
        설정된 로거 인스턴스
    """
    # 환경 변수에서 로그 레벨 가져오기
    if log_level is None:
        log_level = os.environ.get('LOG_LEVEL', 'INFO')
    
    # 로그 레벨 문자열을 로깅 모듈 상수로 변환
    log_level = LOG_LEVELS.get(log_level.upper(), logging.INFO)
    
    # 환경 변수에서 로그 디렉토리 가져오기
    if log_dir is None:
        log_dir = os.environ.get('LOG_DIR', 'logs')
    
    # 로그 디렉토리 생성
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 로거 생성
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 이미 핸들러가 설정되어 있으면 중복 설정 방지
    if not logger.handlers:
        # 🟢 일별 로테이션 파일 핸들러 (성능 최적화)
        log_file = os.path.join(log_dir, f'{name}.log')
        file_handler = TimedRotatingFileHandler(
            log_file,
            when='midnight',
            interval=1,
            backupCount=30,  # 30일 보관
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        
        # 콘솔 핸들러
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        
        # 로거에 핸들러 추가
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger

def log_to_file(log_data, log_file='custom_logs.json', log_dir='logs'):
    """
    사용자 정의 로그를 JSON 파일에 저장
    
    Args:
        log_data: 로그 데이터 (딕셔너리)
        log_file: 로그 파일 이름
        log_dir: 로그 파일 디렉토리
        
    Returns:
        성공 여부
    """
    try:
        # 로그 디렉토리 생성
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 로그 파일 경로
        log_path = os.path.join(log_dir, log_file)
        
        # 현재 시간 추가
        log_data['timestamp'] = datetime.now().isoformat()
        
        # 로그 파일이 있는지 확인
        if os.path.exists(log_path):
            # 기존 로그 파일 읽기
            with open(log_path, 'r') as f:
                try:
                    logs = json.load(f)
                except json.JSONDecodeError:
                    logs = []
        else:
            logs = []
        
        # 로그 추가
        if isinstance(logs, list):
            logs.append(log_data)
        else:
            logs = [log_data]
        
        # 로그 파일 쓰기
        with open(log_path, 'w') as f:
            json.dump(logs, f, indent=2)
        
        return True
    except Exception as e:
        print(f"Error logging to file: {e}")
        traceback.print_exc()
        return False

def log_exception(logger, e, context=None):
    """
    예외 로깅 헬퍼 함수
    
    Args:
        logger: 로거 인스턴스
        e: 예외 객체
        context: 추가 컨텍스트 정보 (딕셔너리)
    """
    error_info = {
        'error': str(e),
        'type': type(e).__name__,
        'traceback': traceback.format_exc()
    }
    
    if context:
        error_info.update(context)
    
    # 로그 메시지 생성
    message = f"Error: {error_info['error']} ({error_info['type']})"
    if context:
        context_str = ', '.join(f"{k}={v}" for k, v in context.items())
        message += f" - Context: {context_str}"
    
    # 로그 출력
    logger.error(message)
    
    # 상세 로그 파일에 저장
    log_to_file(error_info, log_file='errors.json')